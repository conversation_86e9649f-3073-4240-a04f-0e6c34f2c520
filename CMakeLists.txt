cmake_minimum_required(VERSION 3.16)
project(ARINC424Viewer VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Build type" FORCE)
endif()

# Enable debug symbols in debug builds
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
endif()

# Set up Qt MOC before finding packages
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Find required packages
find_package(Qt5 REQUIRED COMPONENTS Core Widgets WebEngineWidgets)
if(Qt5_FOUND)
    message(STATUS "Found Qt5: ${Qt5_VERSION}")
endif()

find_package(PkgConfig REQUIRED)
pkg_check_modules(PYTHON REQUIRED python3-embed)
if(PYTHON_FOUND)
    message(STATUS "Found Python: ${PYTHON_VERSION}")
endif()

# Define source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/PythonInterface.cpp
    src/DataModels.cpp
    src/MapWidget.cpp
    src/FilterWidget.cpp
)

set(HEADERS
    src/MainWindow.h
    src/PythonInterface.h
    src/DataModels.h
    src/MapWidget.h
    src/FilterWidget.h
)

# Add executable
add_executable(arinc424_viewer ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(arinc424_viewer
    Qt5::Core
    Qt5::Widgets
    Qt5::WebEngineWidgets
    ${PYTHON_LIBRARIES}
)

# Include directories
target_include_directories(arinc424_viewer PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${PYTHON_INCLUDE_DIRS}
)

# Compiler flags
target_compile_options(arinc424_viewer PRIVATE ${PYTHON_CFLAGS_OTHER})

# Add compiler warnings
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(arinc424_viewer PRIVATE
        -Wall -Wextra -Wpedantic -Wno-unused-parameter)
endif()

# Set target properties
set_target_properties(arinc424_viewer PROPERTIES
    OUTPUT_NAME "arinc424_viewer"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}"
)

# Copy Python scripts to build directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/python)
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/python/arinc_parser.py
               ${CMAKE_CURRENT_BINARY_DIR}/python/arinc_parser.py COPYONLY)

# Copy ARINC 424 module to build directory
file(GLOB_RECURSE ARINC_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/src/arinc424/*")
foreach(ARINC_FILE ${ARINC_SOURCES})
    file(RELATIVE_PATH REL_PATH "${CMAKE_CURRENT_SOURCE_DIR}/src" ${ARINC_FILE})
    configure_file(${ARINC_FILE} ${CMAKE_CURRENT_BINARY_DIR}/${REL_PATH} COPYONLY)
endforeach()
