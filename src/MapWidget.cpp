/**
 * @file MapWidget.cpp
 * @brief Enhanced map widget implementation for ARINC 424 aviation data visualization
 */

#include "MapWidget.h"
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QApplication>
#include <QMessageBox>
#include <QFileDialog>
#include <QStandardPaths>
#include <QDir>
#include <QSplitter>
#include <QScrollArea>
#include <QGridLayout>
#include <QSpacerItem>
#include <QButtonGroup>
#include <QToolTip>
#include <QCursor>
#include <QtMath>
#include <QRegularExpression>
#include <QTextStream>
#include <QBuffer>
#include <QImageWriter>

MapWidget::MapWidget(QWidget* parent)
    : QWidget(parent),
      m_mapReady(false),
      m_dataProcessing(false),
      m_currentZoom(8),
      m_viewportNorth(90), m_viewportSouth(-90),
      m_viewportEast(180), m_viewportWest(-180) {

    // Initialize settings with defaults
    m_settings.enableClustering = true;
    m_settings.clusterRadius = 50;
    m_settings.maxZoomCluster = 15;
    m_settings.showCoordinates = true;
    m_settings.showScale = true;
    m_settings.coordSystem = CoordinateSystem::WGS84_DECIMAL;
    m_settings.basemap = "OpenStreetMap";
    m_settings.opacity = 1.0;
    m_settings.showLayerControl = true;
    m_settings.enableAnimation = true;

    // Initialize layer visibility
    m_layerVisibility[MapLayer::NAVAIDS] = true;
    m_layerVisibility[MapLayer::WAYPOINTS] = true;
    m_layerVisibility[MapLayer::AIRPORTS] = true;
    m_layerVisibility[MapLayer::AIRWAYS] = true;
    m_layerVisibility[MapLayer::PROCEDURES] = true;
    m_layerVisibility[MapLayer::AIRSPACE] = false;
    m_layerVisibility[MapLayer::TERRAIN] = false;
    m_layerVisibility[MapLayer::WEATHER] = false;

    // Initialize layer opacity
    for (auto layer : {MapLayer::NAVAIDS, MapLayer::WAYPOINTS, MapLayer::AIRPORTS,
                       MapLayer::AIRWAYS, MapLayer::PROCEDURES, MapLayer::AIRSPACE,
                       MapLayer::TERRAIN, MapLayer::WEATHER}) {
        m_layerOpacity[layer] = 1.0;
    }

    // Setup update timer
    m_updateTimer = new QTimer(this);
    m_updateTimer->setSingleShot(true);
    m_updateTimer->setInterval(100); // 100ms debounce
    connect(m_updateTimer, &QTimer::timeout, this, &MapWidget::onUpdateTimer);

    setupUI();
}

MapWidget::~MapWidget() {
    if (m_dataProcessingFuture.isRunning()) {
        m_dataProcessingFuture.cancel();
        m_dataProcessingFuture.waitForFinished();
    }
}

void MapWidget::setupUI() {
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(2);

    // Create main splitter
    QSplitter* mainSplitter = new QSplitter(Qt::Horizontal, this);

    // Setup controls panel
    setupControls();
    QScrollArea* controlsScroll = new QScrollArea();
    controlsScroll->setWidget(createControlsWidget());
    controlsScroll->setWidgetResizable(true);
    controlsScroll->setMaximumWidth(300);
    controlsScroll->setMinimumWidth(250);

    // Setup web view
    m_webView = new QWebEngineView(this);
    m_webView->setMinimumSize(600, 400);

    // Add to splitter
    mainSplitter->addWidget(controlsScroll);
    mainSplitter->addWidget(m_webView);
    mainSplitter->setStretchFactor(0, 0);
    mainSplitter->setStretchFactor(1, 1);

    // Status bar
    QHBoxLayout* statusLayout = new QHBoxLayout();
    m_statusLabel = new QLabel("Initializing map...");
    m_coordinateLabel = new QLabel("Coordinates: --");
    m_measurementLabel = new QLabel("Measurement: --");
    m_loadingProgress = new QProgressBar();
    m_loadingProgress->setVisible(false);
    m_loadingProgress->setMaximumWidth(200);

    statusLayout->addWidget(m_statusLabel);
    statusLayout->addStretch();
    statusLayout->addWidget(m_measurementLabel);
    statusLayout->addWidget(m_coordinateLabel);
    statusLayout->addWidget(m_loadingProgress);

    // Add to main layout
    m_mainLayout->addWidget(mainSplitter);
    m_mainLayout->addLayout(statusLayout);

    setLayout(m_mainLayout);

    // Connect signals
    connect(m_webView, &QWebEngineView::loadFinished, this, &MapWidget::onMapLoadFinished);
}

QWidget* MapWidget::createControlsWidget() {
    QWidget* controlsWidget = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(controlsWidget);
    layout->setContentsMargins(5, 5, 5, 5);
    layout->setSpacing(5);

    // Layer controls
    setupLayerControls();
    layout->addWidget(m_layerControlGroup);

    // Settings
    setupSettingsControls();
    layout->addWidget(m_settingsGroup);

    // Measurement tools
    setupMeasurementTools();
    layout->addWidget(m_measurementGroup);

    // Export tools
    QGroupBox* exportGroup = new QGroupBox("Export");
    QVBoxLayout* exportLayout = new QVBoxLayout(exportGroup);

    m_exportMapBtn = new QPushButton("Export Map");
    m_exportDataBtn = new QPushButton("Export Data");

    connect(m_exportMapBtn, &QPushButton::clicked, [this]() {
        QString filename = QFileDialog::getSaveFileName(this, "Export Map",
            QStandardPaths::writableLocation(QStandardPaths::PicturesLocation) + "/map.png",
            "PNG Images (*.png);;JPEG Images (*.jpg)");
        if (!filename.isEmpty()) {
            exportMap(filename);
        }
    });

    connect(m_exportDataBtn, &QPushButton::clicked, [this]() {
        QString filename = QFileDialog::getSaveFileName(this, "Export Data",
            QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/data.geojson",
            "GeoJSON Files (*.geojson);;KML Files (*.kml)");
        if (!filename.isEmpty()) {
            exportData(filename);
        }
    });

    exportLayout->addWidget(m_exportMapBtn);
    exportLayout->addWidget(m_exportDataBtn);
    layout->addWidget(exportGroup);

    layout->addStretch();
    return controlsWidget;
}

void MapWidget::initializeMap() {
    m_statusLabel->setText("Loading map...");
    m_loadingProgress->setVisible(true);
    m_loadingProgress->setRange(0, 0); // Indeterminate

    QString html = generateMapHTML();
    m_webView->setHtml(html);
}

void MapWidget::setupLayerControls() {
    m_layerControlGroup = new QGroupBox("Layers");
    QVBoxLayout* layout = new QVBoxLayout(m_layerControlGroup);

    // Create layer checkboxes and opacity sliders
    QStringList layerNames = {"Navigation Aids", "Waypoints", "Airports", "Airways",
                             "Procedures", "Airspace", "Terrain", "Weather"};
    QList<MapLayer> layers = {MapLayer::NAVAIDS, MapLayer::WAYPOINTS, MapLayer::AIRPORTS,
                             MapLayer::AIRWAYS, MapLayer::PROCEDURES, MapLayer::AIRSPACE,
                             MapLayer::TERRAIN, MapLayer::WEATHER};

    for (int i = 0; i < layers.size(); ++i) {
        MapLayer layer = layers[i];
        QString name = layerNames[i];

        QHBoxLayout* layerLayout = new QHBoxLayout();

        // Checkbox for visibility
        QCheckBox* checkbox = new QCheckBox(name);
        checkbox->setChecked(m_layerVisibility[layer]);
        m_layerCheckboxes[layer] = checkbox;

        connect(checkbox, &QCheckBox::toggled, [this, layer](bool checked) {
            setLayerVisible(layer, checked);
            emit layerVisibilityChanged(layer, checked);
        });

        // Opacity slider
        QSlider* opacitySlider = new QSlider(Qt::Horizontal);
        opacitySlider->setRange(0, 100);
        opacitySlider->setValue(static_cast<int>(m_layerOpacity[layer] * 100));
        opacitySlider->setMaximumWidth(80);
        opacitySlider->setToolTip("Layer Opacity");
        m_opacitySliders[layer] = opacitySlider;

        connect(opacitySlider, &QSlider::valueChanged, [this, layer](int value) {
            double opacity = value / 100.0;
            setLayerOpacity(layer, opacity);
            emit onOpacityChanged(layer, opacity);
        });

        layerLayout->addWidget(checkbox);
        layerLayout->addStretch();
        layerLayout->addWidget(opacitySlider);

        layout->addLayout(layerLayout);
    }
}

void MapWidget::setupSettingsControls() {
    m_settingsGroup = new QGroupBox("Settings");
    QGridLayout* layout = new QGridLayout(m_settingsGroup);

    int row = 0;

    // Basemap selection
    layout->addWidget(new QLabel("Basemap:"), row, 0);
    m_basemapCombo = new QComboBox();
    m_basemapCombo->addItems({"OpenStreetMap", "Satellite", "Terrain", "Aviation Chart"});
    m_basemapCombo->setCurrentText(m_settings.basemap);
    connect(m_basemapCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &MapWidget::setBasemap);
    layout->addWidget(m_basemapCombo, row++, 1);

    // Coordinate system
    layout->addWidget(new QLabel("Coordinates:"), row, 0);
    m_coordSystemCombo = new QComboBox();
    m_coordSystemCombo->addItems({"Decimal Degrees", "Deg/Min/Sec", "UTM", "MGRS"});
    m_coordSystemCombo->setCurrentIndex(static_cast<int>(m_settings.coordSystem));
    connect(m_coordSystemCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            [this](int index) {
                setCoordinateSystem(static_cast<CoordinateSystem>(index));
            });
    layout->addWidget(m_coordSystemCombo, row++, 1);

    // Clustering
    m_clusteringCheckbox = new QCheckBox("Enable Clustering");
    m_clusteringCheckbox->setChecked(m_settings.enableClustering);
    connect(m_clusteringCheckbox, &QCheckBox::toggled, [this](bool checked) {
        m_settings.enableClustering = checked;
        executeJavaScript(QString("updateClusteringSettings(%1, %2);")
                         .arg(checked ? "true" : "false")
                         .arg(m_settings.clusterRadius));
    });
    layout->addWidget(m_clusteringCheckbox, row++, 0, 1, 2);

    // Cluster radius
    layout->addWidget(new QLabel("Cluster Radius:"), row, 0);
    m_clusterRadiusSlider = new QSlider(Qt::Horizontal);
    m_clusterRadiusSlider->setRange(20, 100);
    m_clusterRadiusSlider->setValue(m_settings.clusterRadius);
    connect(m_clusterRadiusSlider, &QSlider::valueChanged, [this](int value) {
        m_settings.clusterRadius = value;
        executeJavaScript(QString("updateClusteringSettings(%1, %2);")
                         .arg(m_settings.enableClustering ? "true" : "false")
                         .arg(value));
    });
    layout->addWidget(m_clusterRadiusSlider, row++, 1);

    // Animation
    m_animationCheckbox = new QCheckBox("Enable Animation");
    m_animationCheckbox->setChecked(m_settings.enableAnimation);
    connect(m_animationCheckbox, &QCheckBox::toggled, [this](bool checked) {
        m_settings.enableAnimation = checked;
        executeJavaScript(QString("setAnimationEnabled(%1);").arg(checked ? "true" : "false"));
    });
    layout->addWidget(m_animationCheckbox, row++, 0, 1, 2);
}

void MapWidget::setupMeasurementTools() {
    m_measurementGroup = new QGroupBox("Measurement Tools");
    QVBoxLayout* layout = new QVBoxLayout(m_measurementGroup);

    m_measureDistanceBtn = new QPushButton("Measure Distance");
    m_measureAreaBtn = new QPushButton("Measure Area");
    m_clearMeasurementsBtn = new QPushButton("Clear Measurements");

    connect(m_measureDistanceBtn, &QPushButton::clicked, [this]() {
        executeJavaScript("startDistanceMeasurement();");
        m_measureDistanceBtn->setEnabled(false);
        m_measureAreaBtn->setEnabled(false);
    });

    connect(m_measureAreaBtn, &QPushButton::clicked, [this]() {
        executeJavaScript("startAreaMeasurement();");
        m_measureDistanceBtn->setEnabled(false);
        m_measureAreaBtn->setEnabled(false);
    });

    connect(m_clearMeasurementsBtn, &QPushButton::clicked, [this]() {
        executeJavaScript("clearMeasurements();");
        m_measureDistanceBtn->setEnabled(true);
        m_measureAreaBtn->setEnabled(true);
        m_measurementLabel->setText("Measurement: --");
    });

    layout->addWidget(m_measureDistanceBtn);
    layout->addWidget(m_measureAreaBtn);
    layout->addWidget(m_clearMeasurementsBtn);
}

void MapWidget::onMapLoadFinished(bool success) {
    m_loadingProgress->setVisible(false);

    if (success) {
        m_mapReady = true;
        m_statusLabel->setText("Map ready");
        emit mapReady();

        qDebug() << "Map loaded successfully";

        // Plot any pending data
        if (!m_currentRecords.isEmpty()) {
            plotRecords(m_currentRecords);
        }
        if (!m_currentProcedures.isEmpty()) {
            plotProcedures(m_currentProcedures);
        }
    } else {
        m_statusLabel->setText("Map failed to load");
        emit errorOccurred("Failed to load map");
        qWarning() << "Map failed to load";
    }
}

QString MapWidget::generateMapHTML() {
    return R"(
<!DOCTYPE html>
<html>
<head>
    <title>Enhanced ARINC 424 Map</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Leaflet CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Leaflet plugins -->
    <script src="https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.Default.css" />

    <!-- Measurement plugin -->
    <script src="https://unpkg.com/leaflet-measure@3.1.0/dist/leaflet-measure.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet-measure@3.1.0/dist/leaflet-measure.css" />

    <!-- Polyline decorator for arrows -->
    <script src="https://unpkg.com/leaflet-polylinedecorator@1.6.0/dist/leaflet.polylineDecorator.js"></script>

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        #map {
            height: 100vh;
            width: 100%;
            cursor: crosshair;
        }

        .custom-popup {
            font-size: 12px;
            line-height: 1.4;
        }

        .popup-header {
            font-weight: bold;
            color: #2c3e50;
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-bottom: 5px;
        }

        .popup-content {
            color: #34495e;
        }

        .popup-coordinates {
            font-family: monospace;
            background-color: #ecf0f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 11px;
        }

        .procedure-waypoint {
            background-color: rgba(255, 255, 255, 0.9);
            border: 2px solid #3498db;
            border-radius: 50%;
            width: 8px;
            height: 8px;
        }

        .airport-marker {
            background-color: #f39c12;
            border: 2px solid #d68910;
        }

        .navaid-marker {
            background-color: #e74c3c;
            border: 2px solid #c0392b;
        }

        .waypoint-marker {
            background-color: #3498db;
            border: 2px solid #2980b9;
        }

        .coordinate-display {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .measurement-display {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .layer-control {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .cluster-icon {
            background-color: rgba(52, 152, 219, 0.8);
            border: 3px solid rgba(41, 128, 185, 1);
            border-radius: 50%;
            color: white;
            font-weight: bold;
            text-align: center;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <div id="coordinate-display" class="coordinate-display">Coordinates: --</div>
    <div id="measurement-display" class="measurement-display" style="display: none;">Measurement: --</div>

    <script>
        // Global variables
        var map;
        var baseLayers = {};
        var overlayLayers = {};
        var layerControl;
        var measureControl;
        var currentMeasurement = null;
        var clusterGroups = {};
        var settings = {
            enableClustering: true,
            clusterRadius: 50,
            maxZoomCluster: 15,
            enableAnimation: true,
            coordSystem: 'decimal'
        };

        // Layer groups for different data types
        var layers = {
            navaids: null,
            waypoints: null,
            airports: null,
            airways: null,
            procedures: null,
            airspace: null,
            terrain: null,
            weather: null
        };

        // Initialize map
        function initializeMap() {
            map = L.map('map', {
                center: [45.0, -100.0],
                zoom: 4,
                zoomControl: true,
                attributionControl: true
            });

            // Base layers
            baseLayers['OpenStreetMap'] = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            });

            baseLayers['Satellite'] = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                maxZoom: 19
            });

            baseLayers['Terrain'] = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenTopoMap contributors',
                maxZoom: 17
            });

            baseLayers['Aviation Chart'] = L.tileLayer('https://wms.chartbundle.com/tms/1.0.0/sec/{z}/{x}/{y}.png?origin=nw', {
                attribution: '© ChartBundle',
                maxZoom: 12
            });

            // Add default base layer
            baseLayers['OpenStreetMap'].addTo(map);

            // Initialize layer groups
            initializeLayers();

            // Add layer control
            layerControl = L.control.layers(baseLayers, overlayLayers, {
                position: 'topright',
                collapsed: false
            }).addTo(map);

            // Add measurement control
            measureControl = L.control.measure({
                position: 'topleft',
                primaryLengthUnit: 'nauticalMiles',
                secondaryLengthUnit: 'kilometers',
                primaryAreaUnit: 'sqnauticalMiles',
                secondaryAreaUnit: 'sqkilometers'
            });

            // Add scale control
            L.control.scale({
                position: 'bottomright',
                metric: true,
                imperial: true
            }).addTo(map);

            // Mouse coordinate tracking
            map.on('mousemove', function(e) {
                updateCoordinateDisplay(e.latlng);
            });

            // Map events
            map.on('zoomend moveend', function() {
                updateViewport();
                optimizeDisplay();
            });

            // Measurement events
            map.on('measurefinish', function(e) {
                var measurement = '';
                if (e.distance) {
                    measurement = 'Distance: ' + e.distance.toFixed(2) + ' nm';
                } else if (e.area) {
                    measurement = 'Area: ' + e.area.toFixed(2) + ' sq nm';
                }
                updateMeasurementDisplay(measurement);
            });

            console.log('Map initialized successfully');
        }

        function initializeLayers() {
            // Create layer groups with clustering support
            Object.keys(layers).forEach(function(layerName) {
                if (settings.enableClustering &&
                    ['navaids', 'waypoints', 'airports'].includes(layerName)) {

                    clusterGroups[layerName] = L.markerClusterGroup({
                        maxClusterRadius: settings.clusterRadius,
                        disableClusteringAtZoom: settings.maxZoomCluster,
                        spiderfyOnMaxZoom: true,
                        showCoverageOnHover: false,
                        zoomToBoundsOnClick: true,
                        iconCreateFunction: function(cluster) {
                            var count = cluster.getChildCount();
                            var size = count < 10 ? 'small' : count < 100 ? 'medium' : 'large';
                            var sizeMap = { small: 30, medium: 40, large: 50 };

                            return L.divIcon({
                                html: '<div class="cluster-icon" style="width:' + sizeMap[size] + 'px;height:' + sizeMap[size] + 'px;">' + count + '</div>',
                                className: 'marker-cluster marker-cluster-' + size,
                                iconSize: [sizeMap[size], sizeMap[size]]
                            });
                        }
                    });

                    layers[layerName] = clusterGroups[layerName];
                } else {
                    layers[layerName] = L.layerGroup();
                }

                // Add to overlay control
                var displayName = layerName.charAt(0).toUpperCase() + layerName.slice(1);
                overlayLayers[displayName] = layers[layerName];

                // Add to map by default for main layers
                if (['navaids', 'waypoints', 'airports', 'airways', 'procedures'].includes(layerName)) {
                    layers[layerName].addTo(map);
                }
            });
        }

        // Advanced visualization functions
        function addRecordsToLayer(layerName, geoJsonData, styleData) {
            try {
                var geoJson = JSON.parse(geoJsonData);
                var style = JSON.parse(styleData);
                var layer = layers[layerName];

                if (!layer) {
                    console.error('Layer not found:', layerName);
                    return;
                }

                var geoJsonLayer = L.geoJSON(geoJson, {
                    pointToLayer: function(feature, latlng) {
                        var marker = L.circleMarker(latlng, {
                            radius: style.radius || 6,
                            fillColor: style.fillColor || '#3388ff',
                            color: style.color || '#000',
                            weight: style.weight || 2,
                            opacity: style.opacity || 1,
                            fillOpacity: style.fillOpacity || 0.8
                        });

                        // Add popup with formatted content
                        var popupContent = formatRecordPopup(feature.properties);
                        marker.bindPopup(popupContent);

                        // Add click handler
                        marker.on('click', function(e) {
                            window.qtBridge && window.qtBridge.recordClicked(feature.properties.id);
                        });

                        return marker;
                    },
                    onEachFeature: function(feature, layer) {
                        // Store feature data for later access
                        layer.feature = feature;
                    }
                });

                // Add to appropriate layer (clustered or regular)
                if (clusterGroups[layerName]) {
                    clusterGroups[layerName].addLayer(geoJsonLayer);
                } else {
                    layer.addLayer(geoJsonLayer);
                }

            } catch (error) {
                console.error('Error adding records to layer:', error);
            }
        }

        function addProcedureToLayer(geoJsonData, styleData) {
            try {
                var geoJson = JSON.parse(geoJsonData);
                var style = JSON.parse(styleData);
                var layer = layers.procedures;

                var geoJsonLayer = L.geoJSON(geoJson, {
                    style: function(feature) {
                        return {
                            color: style.color || '#3388ff',
                            weight: style.weight || 3,
                            opacity: style.opacity || 0.8,
                            dashArray: style.dashArray || null
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        // Add popup
                        var popupContent = formatProcedurePopup(feature.properties);
                        layer.bindPopup(popupContent);

                        // Add click handler
                        layer.on('click', function(e) {
                            window.qtBridge && window.qtBridge.procedureClicked(feature.properties.id);
                        });

                        // Add directional arrows if enabled
                        if (style.arrowheads) {
                            var decorator = L.polylineDecorator(layer, {
                                patterns: [{
                                    offset: '100%',
                                    repeat: 0,
                                    symbol: L.Symbol.arrowHead({
                                        pixelSize: style.arrowheadLength || 10,
                                        polygon: false,
                                        pathOptions: {
                                            stroke: true,
                                            weight: 2,
                                            color: style.color
                                        }
                                    })
                                }]
                            });
                            layer.addTo(map);
                        }
                    }
                });

                layer.addLayer(geoJsonLayer);

            } catch (error) {
                console.error('Error adding procedure to layer:', error);
            }
        }

        function addSingleRecord(layerName, geoJsonData, styleData) {
            addRecordsToLayer(layerName, geoJsonData, styleData);
        }

        function addSingleProcedure(geoJsonData, styleData) {
            addProcedureToLayer(geoJsonData, styleData);
        }

        function clearLayer(layerName) {
            var layer = layers[layerName];
            if (layer) {
                layer.clearLayers();
            }

            var clusterGroup = clusterGroups[layerName];
            if (clusterGroup) {
                clusterGroup.clearLayers();
            }
        }

        function clearAllLayers() {
            Object.keys(layers).forEach(function(layerName) {
                clearLayer(layerName);
            });
        }

        function clearRecordLayers() {
            ['navaids', 'waypoints', 'airports', 'airways'].forEach(function(layerName) {
                clearLayer(layerName);
            });
        }

        function setLayerVisible(layerName, visible) {
            var layer = layers[layerName];
            if (!layer) return;

            if (visible) {
                if (!map.hasLayer(layer)) {
                    layer.addTo(map);
                }
            } else {
                if (map.hasLayer(layer)) {
                    map.removeLayer(layer);
                }
            }
        }

        function setLayerOpacity(layerName, opacity) {
            var layer = layers[layerName];
            if (!layer) return;

            layer.eachLayer(function(sublayer) {
                if (sublayer.setStyle) {
                    sublayer.setStyle({ opacity: opacity, fillOpacity: opacity * 0.8 });
                }
            });
        }

        function updateClusteringSettings(enabled, radius) {
            settings.enableClustering = enabled;
            settings.clusterRadius = radius;

            // Reinitialize clustering for affected layers
            ['navaids', 'waypoints', 'airports'].forEach(function(layerName) {
                var layer = layers[layerName];
                var clusterGroup = clusterGroups[layerName];

                if (enabled && !clusterGroup) {
                    // Create cluster group
                    clusterGroups[layerName] = L.markerClusterGroup({
                        maxClusterRadius: radius,
                        disableClusteringAtZoom: settings.maxZoomCluster
                    });

                    // Move existing markers to cluster group
                    if (layer) {
                        layer.eachLayer(function(marker) {
                            clusterGroups[layerName].addLayer(marker);
                        });
                        layer.clearLayers();
                        map.removeLayer(layer);
                        clusterGroups[layerName].addTo(map);
                        layers[layerName] = clusterGroups[layerName];
                    }
                } else if (!enabled && clusterGroup) {
                    // Remove clustering
                    var regularLayer = L.layerGroup();
                    clusterGroup.eachLayer(function(marker) {
                        regularLayer.addLayer(marker);
                    });

                    map.removeLayer(clusterGroup);
                    regularLayer.addTo(map);
                    layers[layerName] = regularLayer;
                    delete clusterGroups[layerName];
                }
            });
        }

        function setAnimationEnabled(enabled) {
            settings.enableAnimation = enabled;
            // Update map animation settings
            map.options.zoomAnimation = enabled;
            map.options.markerZoomAnimation = enabled;
        }

        function setBasemap(basemapName) {
            // Remove current base layer
            map.eachLayer(function(layer) {
                if (baseLayers[Object.keys(baseLayers).find(key => baseLayers[key] === layer)]) {
                    map.removeLayer(layer);
                }
            });

            // Add new base layer
            if (baseLayers[basemapName]) {
                baseLayers[basemapName].addTo(map);
            }
        }

        function setCoordinateSystem(system) {
            settings.coordSystem = system;
            // Update coordinate display format
            updateCoordinateDisplay(map.getCenter());
        }

        function setMapCenter(lat, lng, zoom) {
            map.setView([lat, lng], zoom, {
                animate: settings.enableAnimation
            });
        }

        function fitBounds(north, south, east, west) {
            var bounds = L.latLngBounds([south, west], [north, east]);
            map.fitBounds(bounds, {
                animate: settings.enableAnimation,
                padding: [20, 20]
            });
        }

        function getMapBounds() {
            var bounds = map.getBounds();
            return {
                north: bounds.getNorth(),
                south: bounds.getSouth(),
                east: bounds.getEast(),
                west: bounds.getWest()
            };
        }

        function getCurrentCoordinates() {
            var center = map.getCenter();
            return formatCoordinateForSystem(center, settings.coordSystem);
        }

        function updateCoordinateDisplay(latlng) {
            var coordStr = formatCoordinateForSystem(latlng, settings.coordSystem);
            document.getElementById('coordinate-display').innerHTML = 'Coordinates: ' + coordStr;

            // Notify Qt
            if (window.qtBridge) {
                window.qtBridge.coordinateUpdate(coordStr);
            }
        }

        function updateMeasurementDisplay(measurement) {
            var display = document.getElementById('measurement-display');
            if (measurement) {
                display.innerHTML = measurement;
                display.style.display = 'block';
            } else {
                display.style.display = 'none';
            }
        }

        function formatCoordinateForSystem(latlng, system) {
            switch (system) {
                case 'decimal':
                    return latlng.lat.toFixed(6) + ', ' + latlng.lng.toFixed(6);
                case 'dms':
                    return formatDMS(latlng.lat, 'lat') + ', ' + formatDMS(latlng.lng, 'lng');
                case 'utm':
                    return 'UTM: ' + latlng.lat.toFixed(0) + ', ' + latlng.lng.toFixed(0);
                case 'mgrs':
                    return 'MGRS: ' + latlng.lat.toFixed(0) + latlng.lng.toFixed(0);
                default:
                    return latlng.lat.toFixed(6) + ', ' + latlng.lng.toFixed(6);
            }
        }

        function formatDMS(coord, type) {
            var abs = Math.abs(coord);
            var degrees = Math.floor(abs);
            var minutes = Math.floor((abs - degrees) * 60);
            var seconds = ((abs - degrees) * 60 - minutes) * 60;
            var direction = coord >= 0 ? (type === 'lat' ? 'N' : 'E') : (type === 'lat' ? 'S' : 'W');

            return degrees + '° ' + minutes + '\' ' + seconds.toFixed(2) + '" ' + direction;
        }

        function formatRecordPopup(properties) {
            var content = '<div class="custom-popup">';
            content += '<div class="popup-header">' + (properties.name || 'Unknown') + '</div>';
            content += '<div class="popup-content">';
            content += '<strong>Type:</strong> ' + getRecordTypeName(properties.type) + '<br>';
            content += '<strong>Identifier:</strong> ' + (properties.id || 'N/A') + '<br>';
            content += '<strong>Region:</strong> ' + (properties.customerAreaCode || 'N/A') + '<br>';

            if (properties.sequenceNumber >= 0) {
                content += '<strong>Sequence:</strong> ' + properties.sequenceNumber + '<br>';
            }

            content += '</div></div>';
            return content;
        }

        function formatProcedurePopup(properties) {
            var content = '<div class="custom-popup">';
            content += '<div class="popup-header">' + (properties.id || 'Unknown Procedure') + '</div>';
            content += '<div class="popup-content">';
            content += '<strong>Type:</strong> ' + getRecordTypeName(properties.type) + '<br>';
            content += '<strong>Airport:</strong> ' + (properties.airportCode || 'N/A') + '<br>';
            content += '<strong>Waypoints:</strong> ' + (properties.waypointCount || 0) + '<br>';
            content += '</div></div>';
            return content;
        }

        function getRecordTypeName(type) {
            var typeNames = {
                28: 'VHF NAVAID',
                29: 'NDB NAVAID',
                30: 'Waypoint',
                45: 'Airport',
                51: 'SID',
                52: 'STAR',
                53: 'Approach'
            };
            return typeNames[type] || 'Unknown';
        }

        function setDetailLevel(level) {
            // Adjust display detail based on zoom level
            var radius, weight;

            switch (level) {
                case 'high':
                    radius = 8;
                    weight = 3;
                    break;
                case 'medium':
                    radius = 6;
                    weight = 2;
                    break;
                case 'low':
                    radius = 4;
                    weight = 1;
                    break;
            }

            // Update existing markers
            Object.values(layers).forEach(function(layer) {
                if (layer && layer.eachLayer) {
                    layer.eachLayer(function(sublayer) {
                        if (sublayer.setRadius) {
                            sublayer.setRadius(radius);
                        }
                        if (sublayer.setStyle) {
                            sublayer.setStyle({ weight: weight });
                        }
                    });
                }
            });
        }

        // Measurement tools
        function startDistanceMeasurement() {
            if (measureControl) {
                measureControl.addTo(map);
            }
        }

        function startAreaMeasurement() {
            if (measureControl) {
                measureControl.addTo(map);
            }
        }

        function clearMeasurements() {
            if (measureControl && map.hasLayer(measureControl)) {
                map.removeControl(measureControl);
                measureControl.addTo(map);
            }
            updateMeasurementDisplay(null);
        }

        function removeRecord(identifier) {
            Object.values(layers).forEach(function(layer) {
                if (layer && layer.eachLayer) {
                    layer.eachLayer(function(sublayer) {
                        if (sublayer.feature && sublayer.feature.properties.id === identifier) {
                            layer.removeLayer(sublayer);
                        }
                    });
                }
            });
        }

        function exportMap(filename, format) {
            // Basic map export functionality
            console.log('Exporting map to:', filename, 'format:', format);
            // Implementation would depend on specific export library
        }

        // Enhanced procedure visualization functions
        function addEnhancedProcedure(geoJsonData, styleData) {
            try {
                var geoJson = JSON.parse(geoJsonData);
                var style = JSON.parse(styleData);
                var layer = layers.procedures;

                var procedureGroup = L.layerGroup();

                geoJson.features.forEach(function(feature) {
                    if (feature.properties.isMainPath) {
                        // Create main procedure line with arrows
                        var line = L.geoJSON(feature, {
                            style: function(feature) {
                                return {
                                    color: style.color,
                                    weight: style.weight,
                                    opacity: style.opacity,
                                    dashArray: style.dashArray
                                };
                            },
                            onEachFeature: function(feature, layer) {
                                var popupContent = formatProcedurePopup(feature.properties);
                                layer.bindPopup(popupContent);

                                layer.on('click', function(e) {
                                    window.qtBridge && window.qtBridge.procedureClicked(feature.properties.id);
                                });
                            }
                        });

                        procedureGroup.addLayer(line);

                        // Add directional arrows
                        if (style.arrowSize > 0) {
                            line.eachLayer(function(pathLayer) {
                                var decorator = L.polylineDecorator(pathLayer, {
                                    patterns: [
                                        {
                                            offset: '10%',
                                            repeat: '20%',
                                            symbol: L.Symbol.arrowHead({
                                                pixelSize: style.arrowSize,
                                                polygon: false,
                                                pathOptions: {
                                                    stroke: true,
                                                    weight: 2,
                                                    color: style.arrowColor,
                                                    opacity: 0.8
                                                }
                                            })
                                        }
                                    ]
                                });
                                procedureGroup.addLayer(decorator);
                            });
                        }

                    } else if (feature.properties.isWaypoint) {
                        // Create waypoint markers with sequence numbers
                        var waypointStyle = style.waypointStyle;

                        // Use special styling for first/last waypoints
                        if (feature.properties.isFirst) {
                            waypointStyle = style.firstWaypointStyle;
                        } else if (feature.properties.isLast) {
                            waypointStyle = style.lastWaypointStyle;
                        }

                        var waypoint = L.circleMarker([feature.geometry.coordinates[1], feature.geometry.coordinates[0]], {
                            radius: waypointStyle.radius,
                            fillColor: waypointStyle.fillColor,
                            color: waypointStyle.color,
                            weight: waypointStyle.weight,
                            opacity: waypointStyle.opacity,
                            fillOpacity: waypointStyle.fillOpacity
                        });

                        // Add sequence number label
                        var sequenceLabel = L.divIcon({
                            className: 'sequence-label',
                            html: '<div style="background: rgba(255,255,255,0.9); border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 12px; border: 2px solid ' + waypointStyle.fillColor + ';">' + feature.properties.sequence + '</div>',
                            iconSize: [20, 20],
                            iconAnchor: [10, 10]
                        });

                        var sequenceMarker = L.marker([feature.geometry.coordinates[1], feature.geometry.coordinates[0]], {
                            icon: sequenceLabel,
                            zIndexOffset: 1000
                        });

                        // Popup for waypoint
                        var waypointPopup = '<div class="custom-popup">';
                        waypointPopup += '<div class="popup-header">' + feature.properties.name + '</div>';
                        waypointPopup += '<div class="popup-content">';
                        waypointPopup += '<strong>Sequence:</strong> ' + feature.properties.sequence + '<br>';
                        waypointPopup += '<strong>Procedure:</strong> ' + feature.properties.procedureId + '<br>';
                        if (feature.properties.routeType) {
                            waypointPopup += '<strong>Route Type:</strong> ' + feature.properties.routeType + '<br>';
                        }
                        waypointPopup += '</div></div>';

                        waypoint.bindPopup(waypointPopup);
                        sequenceMarker.bindPopup(waypointPopup);

                        procedureGroup.addLayer(waypoint);
                        procedureGroup.addLayer(sequenceMarker);
                    }
                });

                layer.addLayer(procedureGroup);

                // Add animation if enabled
                if (style.enableAnimation) {
                    animateProcedurePath(procedureGroup, style.animationDuration);
                }

            } catch (error) {
                console.error('Error adding enhanced procedure:', error);
            }
        }

        function animateProcedurePath(procedureGroup, duration) {
            // Simple animation that highlights the path
            var originalOpacity = 0.9;
            var highlightOpacity = 1.0;
            var steps = 20;
            var stepDuration = duration / steps;
            var currentStep = 0;

            function animate() {
                var opacity = originalOpacity + (highlightOpacity - originalOpacity) *
                             Math.sin((currentStep / steps) * Math.PI);

                procedureGroup.eachLayer(function(layer) {
                    if (layer.setStyle) {
                        layer.setStyle({ opacity: opacity });
                    }
                });

                currentStep++;
                if (currentStep <= steps) {
                    setTimeout(animate, stepDuration);
                } else {
                    // Reset to original opacity
                    procedureGroup.eachLayer(function(layer) {
                        if (layer.setStyle) {
                            layer.setStyle({ opacity: originalOpacity });
                        }
                    });
                }
            }

            animate();
        }

        function updateProcedureDetailLevel(level, showWaypoints, showSequenceNumbers, showArrows) {
            var procedureLayer = layers.procedures;
            if (!procedureLayer) return;

            procedureLayer.eachLayer(function(group) {
                group.eachLayer(function(layer) {
                    if (layer.options && layer.options.className === 'sequence-label') {
                        // Show/hide sequence numbers
                        if (showSequenceNumbers) {
                            layer.setOpacity(1);
                        } else {
                            layer.setOpacity(0);
                        }
                    } else if (layer instanceof L.CircleMarker) {
                        // Show/hide waypoint markers
                        if (showWaypoints) {
                            layer.setStyle({ opacity: 1, fillOpacity: 0.9 });
                        } else {
                            layer.setStyle({ opacity: 0, fillOpacity: 0 });
                        }
                    } else if (layer._decorators) {
                        // Show/hide arrows
                        if (showArrows) {
                            layer.setStyle({ opacity: 0.8 });
                        } else {
                            layer.setStyle({ opacity: 0 });
                        }
                    }
                });
            });
        }

        function highlightProcedure(procedureId, highlight) {
            var procedureLayer = layers.procedures;
            if (!procedureLayer) return;

            procedureLayer.eachLayer(function(group) {
                group.eachLayer(function(layer) {
                    if (layer.feature && layer.feature.properties.id === procedureId) {
                        if (highlight) {
                            layer.setStyle({
                                weight: 6,
                                opacity: 1.0,
                                color: '#ffff00'
                            });
                        } else {
                            // Reset to original style
                            var originalColor = getRecordColor(layer.feature.properties.type);
                            layer.setStyle({
                                weight: 4,
                                opacity: 0.9,
                                color: originalColor
                            });
                        }
                    }
                });
            });
        }

        // Make functions available to Qt
        window.addRecordsToLayer = addRecordsToLayer;
        window.addProcedureToLayer = addProcedureToLayer;
        window.addSingleRecord = addSingleRecord;
        window.addSingleProcedure = addSingleProcedure;
        window.clearLayer = clearLayer;
        window.clearAllLayers = clearAllLayers;
        window.clearRecordLayers = clearRecordLayers;
        window.setLayerVisible = setLayerVisible;
        window.setLayerOpacity = setLayerOpacity;
        window.updateClusteringSettings = updateClusteringSettings;
        window.setAnimationEnabled = setAnimationEnabled;
        window.setBasemap = setBasemap;
        window.setCoordinateSystem = setCoordinateSystem;
        window.setMapCenter = setMapCenter;
        window.fitBounds = fitBounds;
        window.getMapBounds = getMapBounds;
        window.getCurrentCoordinates = getCurrentCoordinates;
        window.setDetailLevel = setDetailLevel;
        window.startDistanceMeasurement = startDistanceMeasurement;
        window.startAreaMeasurement = startAreaMeasurement;
        window.clearMeasurements = clearMeasurements;
        window.removeRecord = removeRecord;
        window.exportMap = exportMap;
        window.addEnhancedProcedure = addEnhancedProcedure;
        window.updateProcedureDetailLevel = updateProcedureDetailLevel;
        window.highlightProcedure = highlightProcedure;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeMap();
        });
    </script>
</body>
</html>
)";
}

void MapWidget::clearMap() {
    if (!m_mapReady) return;

    executeJavaScript("clearAllLayers();");
    m_currentRecords.clear();
    m_currentProcedures.clear();
    m_recordCache.clear();
    m_procedureCache.clear();
    m_visibleRecords.clear();
    m_visibleProcedures.clear();
}

void MapWidget::clearLayer(MapLayer layer) {
    if (!m_mapReady) return;

    QString layerName = mapLayerToString(layer);
    executeJavaScript(QString("clearLayer('%1');").arg(layerName));
}

QString MapWidget::mapLayerToString(MapLayer layer) {
    switch (layer) {
        case MapLayer::NAVAIDS: return "navaids";
        case MapLayer::WAYPOINTS: return "waypoints";
        case MapLayer::AIRPORTS: return "airports";
        case MapLayer::AIRWAYS: return "airways";
        case MapLayer::PROCEDURES: return "procedures";
        case MapLayer::AIRSPACE: return "airspace";
        case MapLayer::TERRAIN: return "terrain";
        case MapLayer::WEATHER: return "weather";
        default: return "unknown";
    }
}

void MapWidget::executeJavaScript(const QString& script) {
    if (!m_mapReady) {
        qWarning() << "Map not ready, queuing JavaScript:" << script.left(100) + "...";
        return;
    }

    m_webView->page()->runJavaScript(script, [script](const QVariant& result) {
        if (result.isValid() && result.canConvert<QString>()) {
            QString error = result.toString();
            if (!error.isEmpty() && error.contains("Error")) {
                qWarning() << "JavaScript error:" << error;
            }
        }
    });
}

void MapWidget::executeJavaScriptAsync(const QString& script) {
    if (!m_mapReady) return;

    QTimer::singleShot(0, [this, script]() {
        executeJavaScript(script);
    });
}

QVariant MapWidget::executeJavaScriptSync(const QString& script) {
    if (!m_mapReady) return QVariant();

    QVariant result;
    QEventLoop loop;

    m_webView->page()->runJavaScript(script, [&result, &loop](const QVariant& res) {
        result = res;
        loop.quit();
    });

    loop.exec();
    return result;
}

void MapWidget::plotRecords(const QVector<ArincRecord>& records) {
    if (records.isEmpty()) return;

    m_currentRecords = records;

    if (!m_mapReady) {
        qDebug() << "Map not ready, records will be plotted when ready";
        return;
    }

    // Show loading progress for large datasets
    if (records.size() > 1000) {
        m_loadingProgress->setVisible(true);
        m_loadingProgress->setRange(0, 100);
        m_statusLabel->setText(QString("Processing %1 records...").arg(records.size()));
        emit loadingProgress(0);
    }

    // Process data asynchronously for better performance
    if (records.size() > 5000) {
        processDataAsync(records);
    } else {
        processRecordsImmediate(records);
    }
}

void MapWidget::processRecordsImmediate(const QVector<ArincRecord>& records) {
    // Clear existing records
    executeJavaScript("clearRecordLayers();");

    // Group records by type for efficient processing
    QHash<RecordType, QVector<ArincRecord>> recordsByType;
    for (const auto& record : records) {
        if (record.coordinate.isValid()) {
            recordsByType[record.type].append(record);
            m_recordCache[record.identifier] = record;
        }
    }

    // Process each type
    int processed = 0;
    int total = records.size();

    for (auto it = recordsByType.begin(); it != recordsByType.end(); ++it) {
        RecordType type = it.key();
        const QVector<ArincRecord>& typeRecords = it.value();

        QString geoJson = recordsToGeoJSON(typeRecords);
        QString layerName = getLayerForRecordType(type);
        QString style = getRecordStyle(type);

        QString script = QString("addRecordsToLayer('%1', %2, %3);")
                        .arg(layerName)
                        .arg(geoJson)
                        .arg(style);

        executeJavaScript(script);

        processed += typeRecords.size();
        if (total > 1000) {
            emit loadingProgress((processed * 100) / total);
        }
    }

    // Auto-fit map if requested
    if (!records.isEmpty()) {
        zoomToExtent(records);
    }

    m_loadingProgress->setVisible(false);
    m_statusLabel->setText(QString("Displaying %1 records").arg(records.size()));
    emit loadingProgress(100);
}

void MapWidget::processDataAsync(const QVector<ArincRecord>& records) {
    if (m_dataProcessingFuture.isRunning()) {
        m_dataProcessingFuture.cancel();
        m_dataProcessingFuture.waitForFinished();
    }

    m_dataProcessing = true;

    m_dataProcessingFuture = QtConcurrent::run([this, records]() {
        QMutexLocker locker(&m_dataMutex);

        // Process in chunks to avoid blocking
        const int chunkSize = 1000;
        int totalChunks = (records.size() + chunkSize - 1) / chunkSize;

        for (int chunk = 0; chunk < totalChunks; ++chunk) {
            if (m_dataProcessingFuture.isCanceled()) {
                break;
            }

            int start = chunk * chunkSize;
            int end = qMin(start + chunkSize, records.size());

            QVector<ArincRecord> chunkRecords;
            for (int i = start; i < end; ++i) {
                chunkRecords.append(records[i]);
            }

            // Process chunk on main thread
            QMetaObject::invokeMethod(this, [this, chunkRecords]() {
                processRecordsImmediate(chunkRecords);
            }, Qt::QueuedConnection);

            // Update progress
            int progress = ((chunk + 1) * 100) / totalChunks;
            QMetaObject::invokeMethod(this, [this, progress]() {
                emit loadingProgress(progress);
            }, Qt::QueuedConnection);

            // Small delay to keep UI responsive
            QThread::msleep(10);
        }

        m_dataProcessing = false;
        QMetaObject::invokeMethod(this, &MapWidget::onDataProcessingFinished, Qt::QueuedConnection);
    });
}

void MapWidget::onDataProcessingFinished() {
    m_loadingProgress->setVisible(false);
    m_statusLabel->setText(QString("Displaying %1 records").arg(m_currentRecords.size()));
    emit loadingProgress(100);
}

QString MapWidget::getLayerForRecordType(RecordType type) {
    switch (type) {
        case RecordType::VHF_NAVAID:
        case RecordType::NDB_NAVAID:
            return "navaids";
        case RecordType::WAYPOINT:
            return "waypoints";
        case RecordType::AIRPORT:
        case RecordType::HELIPORT:
            return "airports";
        case RecordType::ENROUTE_AIRWAYS:
        case RecordType::AIRWAYS_MARKER:
            return "airways";
        case RecordType::SID:
        case RecordType::STAR:
        case RecordType::APPROACH:
            return "procedures";
        case RecordType::CONTROLLED_AIRSPACE:
        case RecordType::RESTRICTIVE_AIRSPACE:
            return "airspace";
        default:
            return "waypoints";
    }
}

void MapWidget::plotProcedures(const QVector<ProcedurePath>& procedures) {
    if (procedures.isEmpty()) return;

    m_currentProcedures = procedures;

    if (!m_mapReady) {
        qDebug() << "Map not ready, procedures will be plotted when ready";
        return;
    }

    // Clear existing procedures
    executeJavaScript("clearLayer('procedures');");

    for (const auto& procedure : procedures) {
        if (!procedure.isValid()) continue;

        m_procedureCache[procedure.procedureId] = procedure;

        QString geoJson = procedureToGeoJSON(procedure);
        QString style = getProcedureStyle(procedure.procedureType);

        QString script = QString("addProcedureToLayer(%1, %2);")
                        .arg(geoJson)
                        .arg(style);

        executeJavaScript(script);
    }

    m_statusLabel->setText(QString("Displaying %1 procedures").arg(procedures.size()));
}

void MapWidget::addRecord(const ArincRecord& record) {
    if (!record.isValid() || !record.coordinate.isValid()) return;

    m_recordCache[record.identifier] = record;

    if (!m_mapReady) return;

    QString layerName = getLayerForRecordType(record.type);
    QString geoJson = recordToGeoJSON(record);
    QString style = getRecordStyle(record.type);

    QString script = QString("addSingleRecord('%1', %2, %3);")
                    .arg(layerName)
                    .arg(geoJson)
                    .arg(style);

    executeJavaScript(script);
}

void MapWidget::addProcedure(const ProcedurePath& procedure) {
    if (!procedure.isValid()) return;

    m_procedureCache[procedure.procedureId] = procedure;

    if (!m_mapReady) return;

    QString geoJson = procedureToGeoJSON(procedure);
    QString style = getProcedureStyle(procedure.procedureType);

    QString script = QString("addSingleProcedure(%1, %2);")
                    .arg(geoJson)
                    .arg(style);

    executeJavaScript(script);
}

void MapWidget::updateRecord(const ArincRecord& record) {
    if (!record.isValid()) return;

    removeRecord(record.identifier);
    addRecord(record);
}

void MapWidget::removeRecord(const QString& identifier) {
    m_recordCache.remove(identifier);
    m_visibleRecords.remove(identifier);

    if (!m_mapReady) return;

    executeJavaScript(QString("removeRecord('%1');").arg(identifier));
}

void MapWidget::setMapCenter(const QGeoCoordinate& center, int zoom) {
    if (!center.isValid()) return;

    m_currentCenter = center;
    m_currentZoom = zoom;

    if (!m_mapReady) return;

    QString script = QString("setMapCenter(%1, %2, %3);")
                    .arg(center.latitude())
                    .arg(center.longitude())
                    .arg(zoom);

    executeJavaScript(script);
}

void MapWidget::zoomToExtent(const QVector<ArincRecord>& records) {
    if (records.isEmpty()) return;

    double minLat = 90, maxLat = -90, minLng = 180, maxLng = -180;
    bool hasValidCoords = false;

    for (const auto& record : records) {
        if (record.coordinate.isValid()) {
            minLat = qMin(minLat, record.coordinate.latitude());
            maxLat = qMax(maxLat, record.coordinate.latitude());
            minLng = qMin(minLng, record.coordinate.longitude());
            maxLng = qMax(maxLng, record.coordinate.longitude());
            hasValidCoords = true;
        }
    }

    if (hasValidCoords) {
        fitBounds(maxLat, minLat, maxLng, minLng);
    }
}

void MapWidget::zoomToExtent(const QVector<ProcedurePath>& procedures) {
    if (procedures.isEmpty()) return;

    double minLat = 90, maxLat = -90, minLng = 180, maxLng = -180;
    bool hasValidCoords = false;

    for (const auto& procedure : procedures) {
        for (const auto& waypoint : procedure.waypoints) {
            if (waypoint.coordinate.isValid()) {
                minLat = qMin(minLat, waypoint.coordinate.latitude());
                maxLat = qMax(maxLat, waypoint.coordinate.latitude());
                minLng = qMin(minLng, waypoint.coordinate.longitude());
                maxLng = qMax(maxLng, waypoint.coordinate.longitude());
                hasValidCoords = true;
            }
        }
    }

    if (hasValidCoords) {
        fitBounds(maxLat, minLat, maxLng, minLng);
    }
}

void MapWidget::zoomToAirport(const QString& airportCode) {
    // Find airport in current records
    for (const auto& record : m_currentRecords) {
        if (record.type == RecordType::AIRPORT &&
            record.identifier.compare(airportCode, Qt::CaseInsensitive) == 0 &&
            record.coordinate.isValid()) {
            setMapCenter(record.coordinate, 12);
            return;
        }
    }

    qWarning() << "Airport not found:" << airportCode;
}

void MapWidget::fitBounds(double north, double south, double east, double west) {
    if (!m_mapReady) return;

    QString script = QString("fitBounds(%1, %2, %3, %4);")
                    .arg(north).arg(south).arg(east).arg(west);

    executeJavaScript(script);
}

void MapWidget::setLayerVisible(MapLayer layer, bool visible) {
    m_layerVisibility[layer] = visible;

    if (!m_mapReady) return;

    QString layerName = mapLayerToString(layer);
    executeJavaScript(QString("setLayerVisible('%1', %2);")
                     .arg(layerName)
                     .arg(visible ? "true" : "false"));
}

bool MapWidget::isLayerVisible(MapLayer layer) const {
    return m_layerVisibility.value(layer, false);
}

void MapWidget::setLayerOpacity(MapLayer layer, double opacity) {
    m_layerOpacity[layer] = qBound(0.0, opacity, 1.0);

    if (!m_mapReady) return;

    QString layerName = mapLayerToString(layer);
    executeJavaScript(QString("setLayerOpacity('%1', %2);")
                     .arg(layerName)
                     .arg(opacity));
}

double MapWidget::getLayerOpacity(MapLayer layer) const {
    return m_layerOpacity.value(layer, 1.0);
}

// Settings and configuration methods
void MapWidget::setMapSettings(const MapSettings& settings) {
    m_settings = settings;

    if (!m_mapReady) return;

    // Update clustering settings
    executeJavaScript(QString("updateClusteringSettings(%1, %2);")
                     .arg(settings.enableClustering ? "true" : "false")
                     .arg(settings.clusterRadius));

    // Update coordinate system
    setCoordinateSystem(settings.coordSystem);

    // Update basemap
    setBasemap(settings.basemap);

    // Update animation
    executeJavaScript(QString("setAnimationEnabled(%1);")
                     .arg(settings.enableAnimation ? "true" : "false"));
}

MapSettings MapWidget::getMapSettings() const {
    return m_settings;
}

void MapWidget::setBasemap(const QString& basemap) {
    m_settings.basemap = basemap;

    if (!m_mapReady) return;

    executeJavaScript(QString("setBasemap('%1');").arg(basemap));
}

void MapWidget::setCoordinateSystem(CoordinateSystem system) {
    m_settings.coordSystem = system;

    if (!m_mapReady) return;

    QString systemName;
    switch (system) {
        case CoordinateSystem::WGS84_DECIMAL: systemName = "decimal"; break;
        case CoordinateSystem::WGS84_DMS: systemName = "dms"; break;
        case CoordinateSystem::UTM: systemName = "utm"; break;
        case CoordinateSystem::MGRS: systemName = "mgrs"; break;
    }

    executeJavaScript(QString("setCoordinateSystem('%1');").arg(systemName));
}

// Export and utility methods
void MapWidget::exportMap(const QString& filename, const QString& format) {
    if (!m_mapReady) return;

    QString script = QString("exportMap('%1', '%2');").arg(filename).arg(format);
    executeJavaScript(script);
}

void MapWidget::exportData(const QString& filename, const QString& format) {
    if (format.toLower().contains("geojson")) {
        exportGeoJSON(filename);
    } else if (format.toLower().contains("kml")) {
        exportKML(filename);
    }
}

void MapWidget::exportGeoJSON(const QString& filename) {
    QJsonObject root;
    root["type"] = "FeatureCollection";

    QJsonArray features;

    // Add records
    for (const auto& record : m_currentRecords) {
        if (record.coordinate.isValid()) {
            QJsonObject feature = recordToGeoJSONObject(record);
            features.append(feature);
        }
    }

    // Add procedures
    for (const auto& procedure : m_currentProcedures) {
        if (procedure.isValid()) {
            QJsonObject feature = procedureToGeoJSONObject(procedure);
            features.append(feature);
        }
    }

    root["features"] = features;

    QJsonDocument doc(root);

    QFile file(filename);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
        m_statusLabel->setText("Data exported to " + filename);
    } else {
        emit errorOccurred("Failed to export data to " + filename);
    }
}

void MapWidget::exportKML(const QString& filename) {
    // Basic KML export implementation
    QString kml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    kml += "<kml xmlns=\"http://www.opengis.net/kml/2.2\">\n";
    kml += "<Document>\n";
    kml += "<name>ARINC 424 Data</name>\n";

    // Add records as placemarks
    for (const auto& record : m_currentRecords) {
        if (record.coordinate.isValid()) {
            kml += "<Placemark>\n";
            kml += QString("<name>%1</name>\n").arg(record.getDisplayName());
            kml += QString("<description>Type: %1\nIdentifier: %2</description>\n")
                   .arg(static_cast<int>(record.type))
                   .arg(record.identifier);
            kml += "<Point>\n";
            kml += QString("<coordinates>%1,%2,0</coordinates>\n")
                   .arg(record.coordinate.longitude())
                   .arg(record.coordinate.latitude());
            kml += "</Point>\n";
            kml += "</Placemark>\n";
        }
    }

    kml += "</Document>\n";
    kml += "</kml>\n";

    QFile file(filename);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(kml.toUtf8());
        file.close();
        m_statusLabel->setText("Data exported to " + filename);
    } else {
        emit errorOccurred("Failed to export data to " + filename);
    }
}

QString MapWidget::getCurrentCoordinates() const {
    if (!m_mapReady) return QString();

    QVariant result = executeJavaScriptSync("getCurrentCoordinates();");
    return result.toString();
}

double MapWidget::measureDistance(const QGeoCoordinate& start, const QGeoCoordinate& end) const {
    if (!start.isValid() || !end.isValid()) return 0.0;

    // Calculate distance in nautical miles
    double distance = start.distanceTo(end) / 1852.0; // Convert meters to nautical miles
    return distance;
}

// Data conversion methods
QString MapWidget::recordsToGeoJSON(const QVector<ArincRecord>& records) {
    QJsonObject root;
    root["type"] = "FeatureCollection";

    QJsonArray features;
    for (const auto& record : records) {
        if (record.coordinate.isValid()) {
            QJsonObject feature = recordToGeoJSONObject(record);
            features.append(feature);
        }
    }

    root["features"] = features;
    return QJsonDocument(root).toJson(QJsonDocument::Compact);
}

QString MapWidget::proceduresToGeoJSON(const QVector<ProcedurePath>& procedures) {
    QJsonObject root;
    root["type"] = "FeatureCollection";

    QJsonArray features;
    for (const auto& procedure : procedures) {
        if (procedure.isValid()) {
            QJsonObject feature = procedureToGeoJSONObject(procedure);
            features.append(feature);
        }
    }

    root["features"] = features;
    return QJsonDocument(root).toJson(QJsonDocument::Compact);
}

QString MapWidget::recordToGeoJSON(const ArincRecord& record) {
    QJsonObject feature = recordToGeoJSONObject(record);
    return QJsonDocument(feature).toJson(QJsonDocument::Compact);
}

QJsonObject MapWidget::recordToGeoJSONObject(const ArincRecord& record) {
    QJsonObject feature;
    feature["type"] = "Feature";

    // Geometry
    QJsonObject geometry;
    geometry["type"] = "Point";
    QJsonArray coordinates;
    coordinates.append(record.coordinate.longitude());
    coordinates.append(record.coordinate.latitude());
    geometry["coordinates"] = coordinates;
    feature["geometry"] = geometry;

    // Properties
    QJsonObject properties;
    properties["id"] = record.identifier;
    properties["name"] = record.getDisplayName();
    properties["type"] = static_cast<int>(record.type);
    properties["sectionCode"] = record.sectionCode;
    properties["customerAreaCode"] = record.customerAreaCode;
    properties["sequenceNumber"] = record.sequenceNumber;
    properties["procedureIdentifier"] = record.procedureIdentifier;
    properties["routeType"] = record.routeType;

    // Add additional data
    for (auto it = record.additionalData.begin(); it != record.additionalData.end(); ++it) {
        properties[it.key()] = QJsonValue::fromVariant(it.value());
    }

    feature["properties"] = properties;
    return feature;
}

QString MapWidget::procedureToGeoJSON(const ProcedurePath& procedure) {
    QJsonObject feature = procedureToGeoJSONObject(procedure);
    return QJsonDocument(feature).toJson(QJsonDocument::Compact);
}

QJsonObject MapWidget::procedureToGeoJSONObject(const ProcedurePath& procedure) {
    QJsonObject feature;
    feature["type"] = "Feature";

    // Geometry - LineString for the procedure path
    QJsonObject geometry;
    geometry["type"] = "LineString";
    QJsonArray coordinates;

    for (const auto& waypoint : procedure.waypoints) {
        if (waypoint.coordinate.isValid()) {
            QJsonArray coord;
            coord.append(waypoint.coordinate.longitude());
            coord.append(waypoint.coordinate.latitude());
            coordinates.append(coord);
        }
    }

    geometry["coordinates"] = coordinates;
    feature["geometry"] = geometry;

    // Properties
    QJsonObject properties;
    properties["id"] = procedure.procedureId;
    properties["airportCode"] = procedure.airportCode;
    properties["type"] = static_cast<int>(procedure.procedureType);
    properties["waypointCount"] = procedure.waypoints.size();

    // Add waypoint details
    QJsonArray waypoints;
    for (int i = 0; i < procedure.waypoints.size(); ++i) {
        const auto& wp = procedure.waypoints[i];
        QJsonObject waypointObj;
        waypointObj["sequence"] = i + 1;
        waypointObj["name"] = wp.getDisplayName();
        waypointObj["identifier"] = wp.identifier;
        waypointObj["lat"] = wp.coordinate.latitude();
        waypointObj["lng"] = wp.coordinate.longitude();
        waypoints.append(waypointObj);
    }
    properties["waypoints"] = waypoints;

    feature["properties"] = properties;
    return feature;
}

// Styling methods
QString MapWidget::getRecordStyle(RecordType type) {
    QJsonObject style;

    QString color = getRecordColor(type);
    QString icon = getRecordIcon(type);

    style["color"] = color;
    style["fillColor"] = color;
    style["fillOpacity"] = 0.8;
    style["weight"] = 2;
    style["opacity"] = 1.0;
    style["radius"] = getRecordRadius(type);
    style["icon"] = icon;

    return QJsonDocument(style).toJson(QJsonDocument::Compact);
}

QString MapWidget::getRecordIcon(RecordType type) {
    switch (type) {
        case RecordType::VHF_NAVAID: return "radio-tower";
        case RecordType::NDB_NAVAID: return "broadcast-tower";
        case RecordType::WAYPOINT: return "map-pin";
        case RecordType::AIRPORT: return "plane";
        case RecordType::HELIPORT: return "helicopter";
        case RecordType::AIRWAYS_MARKER: return "route";
        case RecordType::HOLDING_PATTERN: return "refresh-cw";
        case RecordType::SID: return "arrow-up-right";
        case RecordType::STAR: return "arrow-down-left";
        case RecordType::APPROACH: return "target";
        default: return "circle";
    }
}

int MapWidget::getRecordRadius(RecordType type) {
    switch (type) {
        case RecordType::AIRPORT:
        case RecordType::HELIPORT:
            return 8;
        case RecordType::VHF_NAVAID:
        case RecordType::NDB_NAVAID:
            return 7;
        case RecordType::WAYPOINT:
            return 5;
        case RecordType::AIRWAYS_MARKER:
            return 4;
        default:
            return 6;
    }
}

QString MapWidget::getRecordColor(RecordType type) {
    switch (type) {
        case RecordType::VHF_NAVAID: return "#FF6B6B";
        case RecordType::NDB_NAVAID: return "#4ECDC4";
        case RecordType::WAYPOINT: return "#45B7D1";
        case RecordType::AIRWAYS_MARKER: return "#96CEB4";
        case RecordType::HOLDING_PATTERN: return "#FFEAA7";
        case RecordType::ENROUTE_AIRWAYS: return "#DDA0DD";
        case RecordType::PREFERRED_ROUTE: return "#98D8C8";
        case RecordType::AIRPORT: return "#F7DC6F";
        case RecordType::HELIPORT: return "#E67E22";
        case RecordType::AIRPORT_NDB: return "#BB8FCE";
        case RecordType::SID: return "#FF9500";        // Orange for SIDs
        case RecordType::STAR: return "#00C851";       // Green for STARs
        case RecordType::APPROACH: return "#FF4444";   // Red for Approaches
        case RecordType::CONTROLLED_AIRSPACE: return "#85C1E9";
        case RecordType::RESTRICTIVE_AIRSPACE: return "#F8C471";
        default: return "#BDC3C7";
    }
}

QString MapWidget::getProcedureStyle(RecordType type) {
    QJsonObject style;

    QString color = getRecordColor(type);

    style["color"] = color;
    style["weight"] = 4;
    style["opacity"] = 0.8;
    style["dashArray"] = (type == RecordType::SID) ? "10, 5" : "";
    style["arrowheads"] = true;
    style["arrowheadLength"] = 10;
    style["arrowheadAngle"] = 45;

    return QJsonDocument(style).toJson(QJsonDocument::Compact);
}

QString MapWidget::formatPopupContent(const ArincRecord& record) {
    QString content = QString("<div class='custom-popup'>");
    content += QString("<div class='popup-header'>%1</div>").arg(record.getDisplayName());
    content += QString("<div class='popup-content'>");
    content += QString("<strong>Type:</strong> %1<br>").arg(recordTypeToString(record.type));
    content += QString("<strong>Identifier:</strong> %1<br>").arg(record.identifier);
    content += QString("<strong>Region:</strong> %1<br>").arg(record.customerAreaCode);

    if (record.coordinate.isValid()) {
        QString coords = formatCoordinate(record.coordinate, m_settings.coordSystem);
        content += QString("<strong>Coordinates:</strong> <span class='popup-coordinates'>%1</span><br>").arg(coords);
    }

    if (record.sequenceNumber >= 0) {
        content += QString("<strong>Sequence:</strong> %1<br>").arg(record.sequenceNumber);
    }

    if (!record.procedureIdentifier.isEmpty()) {
        content += QString("<strong>Procedure:</strong> %1<br>").arg(record.procedureIdentifier);
    }

    content += "</div></div>";
    return content;
}

QString MapWidget::formatPopupContent(const ProcedurePath& procedure) {
    QString content = QString("<div class='custom-popup'>");
    content += QString("<div class='popup-header'>%1</div>").arg(procedure.procedureId);
    content += QString("<div class='popup-content'>");
    content += QString("<strong>Type:</strong> %1<br>").arg(recordTypeToString(procedure.procedureType));
    content += QString("<strong>Airport:</strong> %1<br>").arg(procedure.airportCode);
    content += QString("<strong>Waypoints:</strong> %1<br>").arg(procedure.waypoints.size());

    if (!procedure.waypoints.isEmpty()) {
        content += "<strong>Route:</strong><br>";
        for (int i = 0; i < qMin(5, procedure.waypoints.size()); ++i) {
            const auto& wp = procedure.waypoints[i];
            content += QString("&nbsp;&nbsp;%1. %2<br>").arg(i + 1).arg(wp.getDisplayName());
        }
        if (procedure.waypoints.size() > 5) {
            content += QString("&nbsp;&nbsp;... and %1 more<br>").arg(procedure.waypoints.size() - 5);
        }
    }

    content += "</div></div>";
    return content;
}

QString MapWidget::recordTypeToString(RecordType type) {
    switch (type) {
        case RecordType::VHF_NAVAID: return "VHF NAVAID";
        case RecordType::NDB_NAVAID: return "NDB NAVAID";
        case RecordType::WAYPOINT: return "Waypoint";
        case RecordType::AIRPORT: return "Airport";
        case RecordType::HELIPORT: return "Heliport";
        case RecordType::SID: return "SID";
        case RecordType::STAR: return "STAR";
        case RecordType::APPROACH: return "Approach";
        case RecordType::AIRWAYS_MARKER: return "Airways Marker";
        case RecordType::ENROUTE_AIRWAYS: return "Enroute Airways";
        case RecordType::CONTROLLED_AIRSPACE: return "Controlled Airspace";
        case RecordType::RESTRICTIVE_AIRSPACE: return "Restrictive Airspace";
        default: return "Unknown";
    }
}

// Coordinate formatting methods
QString MapWidget::formatCoordinate(const QGeoCoordinate& coord, CoordinateSystem system) {
    if (!coord.isValid()) return "Invalid";

    switch (system) {
        case CoordinateSystem::WGS84_DECIMAL:
            return QString("%1, %2")
                   .arg(coord.latitude(), 0, 'f', 6)
                   .arg(coord.longitude(), 0, 'f', 6);

        case CoordinateSystem::WGS84_DMS:
            return QString("%1, %2")
                   .arg(coord.toString(QGeoCoordinate::DegreesMinutesSeconds))
                   .arg(coord.toString(QGeoCoordinate::DegreesMinutesSeconds));

        case CoordinateSystem::UTM:
            // Basic UTM conversion (simplified)
            return QString("UTM: %1, %2")
                   .arg(coord.latitude(), 0, 'f', 0)
                   .arg(coord.longitude(), 0, 'f', 0);

        case CoordinateSystem::MGRS:
            // Basic MGRS conversion (simplified)
            return QString("MGRS: %1")
                   .arg(coord.toString(QGeoCoordinate::Degrees));

        default:
            return coord.toString();
    }
}

// Event handlers and slots
void MapWidget::onRecordsUpdated(const QVector<ArincRecord>& records) {
    plotRecords(records);
}

void MapWidget::onProceduresUpdated(const QVector<ProcedurePath>& procedures) {
    plotProcedures(procedures);
}

void MapWidget::onFilterChanged() {
    // Restart update timer to debounce rapid filter changes
    m_updateTimer->start();
}

void MapWidget::onLayerToggled(MapLayer layer, bool visible) {
    setLayerVisible(layer, visible);
}

void MapWidget::onOpacityChanged(MapLayer layer, double opacity) {
    setLayerOpacity(layer, opacity);
}

void MapWidget::onBasemapChanged(const QString& basemap) {
    setBasemap(basemap);
}

void MapWidget::onCoordinateSystemChanged(CoordinateSystem system) {
    setCoordinateSystem(system);
}

void MapWidget::onJavaScriptResult(const QVariant& result) {
    // Handle JavaScript callback results
    if (result.isValid()) {
        QVariantMap resultMap = result.toMap();

        if (resultMap.contains("type")) {
            QString type = resultMap["type"].toString();

            if (type == "coordinateUpdate") {
                QString coords = resultMap["coordinates"].toString();
                m_coordinateLabel->setText("Coordinates: " + coords);
                emit coordinatesChanged(coords);
            }
            else if (type == "measurementUpdate") {
                QString measurement = resultMap["measurement"].toString();
                m_measurementLabel->setText("Measurement: " + measurement);
            }
            else if (type == "extentChanged") {
                double north = resultMap["north"].toDouble();
                double south = resultMap["south"].toDouble();
                double east = resultMap["east"].toDouble();
                double west = resultMap["west"].toDouble();
                emit extentChanged(north, south, east, west);
            }
            else if (type == "recordClicked") {
                QString identifier = resultMap["identifier"].toString();
                if (m_recordCache.contains(identifier)) {
                    emit recordClicked(m_recordCache[identifier]);
                }
            }
            else if (type == "procedureClicked") {
                QString procedureId = resultMap["procedureId"].toString();
                if (m_procedureCache.contains(procedureId)) {
                    emit procedureClicked(m_procedureCache[procedureId]);
                }
            }
        }
    }
}

void MapWidget::onUpdateTimer() {
    // Debounced update - implement any deferred updates here
    updateViewport();
    optimizeForZoomLevel(m_currentZoom);
}

// Performance optimization methods
void MapWidget::updateViewport() {
    if (!m_mapReady) return;

    QVariant result = executeJavaScriptSync("getMapBounds();");
    QVariantMap bounds = result.toMap();

    if (!bounds.isEmpty()) {
        m_viewportNorth = bounds["north"].toDouble();
        m_viewportSouth = bounds["south"].toDouble();
        m_viewportEast = bounds["east"].toDouble();
        m_viewportWest = bounds["west"].toDouble();
    }
}

void MapWidget::optimizeForZoomLevel(int zoomLevel) {
    // Adjust clustering and display based on zoom level
    if (zoomLevel > 12) {
        // High zoom - show more detail, disable clustering for some layers
        executeJavaScript("setDetailLevel('high');");
    } else if (zoomLevel > 8) {
        // Medium zoom - balanced display
        executeJavaScript("setDetailLevel('medium');");
    } else {
        // Low zoom - simplified display, aggressive clustering
        executeJavaScript("setDetailLevel('low');");
    }
}

bool MapWidget::isInViewport(const QGeoCoordinate& coord) {
    if (!coord.isValid()) return false;

    double lat = coord.latitude();
    double lng = coord.longitude();

    return (lat >= m_viewportSouth && lat <= m_viewportNorth &&
            lng >= m_viewportWest && lng <= m_viewportEast);
}

// Additional missing method implementations
void MapWidget::setupControls() {
    // This method is called from setupUI() and creates the control panels
    // The actual implementation is distributed across setupLayerControls(),
    // setupSettingsControls(), and setupMeasurementTools()
}

void MapWidget::setupSettingsControls() {
    m_settingsGroup = new QGroupBox("Settings");
    QGridLayout* layout = new QGridLayout(m_settingsGroup);

    int row = 0;

    // Basemap selection
    layout->addWidget(new QLabel("Basemap:"), row, 0);
    m_basemapCombo = new QComboBox();
    m_basemapCombo->addItems({"OpenStreetMap", "Satellite", "Terrain", "Aviation Chart"});
    m_basemapCombo->setCurrentText(m_settings.basemap);
    connect(m_basemapCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &MapWidget::setBasemap);
    layout->addWidget(m_basemapCombo, row++, 1);

    // Coordinate system
    layout->addWidget(new QLabel("Coordinates:"), row, 0);
    m_coordSystemCombo = new QComboBox();
    m_coordSystemCombo->addItems({"Decimal Degrees", "Deg/Min/Sec", "UTM", "MGRS"});
    m_coordSystemCombo->setCurrentIndex(static_cast<int>(m_settings.coordSystem));
    connect(m_coordSystemCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            [this](int index) {
                setCoordinateSystem(static_cast<CoordinateSystem>(index));
            });
    layout->addWidget(m_coordSystemCombo, row++, 1);

    // Clustering
    m_clusteringCheckbox = new QCheckBox("Enable Clustering");
    m_clusteringCheckbox->setChecked(m_settings.enableClustering);
    connect(m_clusteringCheckbox, &QCheckBox::toggled, [this](bool checked) {
        m_settings.enableClustering = checked;
        executeJavaScript(QString("updateClusteringSettings(%1, %2);")
                         .arg(checked ? "true" : "false")
                         .arg(m_settings.clusterRadius));
    });
    layout->addWidget(m_clusteringCheckbox, row++, 0, 1, 2);

    // Cluster radius
    layout->addWidget(new QLabel("Cluster Radius:"), row, 0);
    m_clusterRadiusSlider = new QSlider(Qt::Horizontal);
    m_clusterRadiusSlider->setRange(20, 100);
    m_clusterRadiusSlider->setValue(m_settings.clusterRadius);
    connect(m_clusterRadiusSlider, &QSlider::valueChanged, [this](int value) {
        m_settings.clusterRadius = value;
        executeJavaScript(QString("updateClusteringSettings(%1, %2);")
                         .arg(m_settings.enableClustering ? "true" : "false")
                         .arg(value));
    });
    layout->addWidget(m_clusterRadiusSlider, row++, 1);

    // Animation
    m_animationCheckbox = new QCheckBox("Enable Animation");
    m_animationCheckbox->setChecked(m_settings.enableAnimation);
    connect(m_animationCheckbox, &QCheckBox::toggled, [this](bool checked) {
        m_settings.enableAnimation = checked;
        executeJavaScript(QString("setAnimationEnabled(%1);").arg(checked ? "true" : "false"));
    });
    layout->addWidget(m_animationCheckbox, row++, 0, 1, 2);
}

QGeoCoordinate MapWidget::parseCoordinate(const QString& coordString, CoordinateSystem system) {
    // Basic coordinate parsing implementation
    QGeoCoordinate coord;

    switch (system) {
        case CoordinateSystem::WGS84_DECIMAL: {
            QStringList parts = coordString.split(',');
            if (parts.size() >= 2) {
                bool latOk, lngOk;
                double lat = parts[0].trimmed().toDouble(&latOk);
                double lng = parts[1].trimmed().toDouble(&lngOk);
                if (latOk && lngOk) {
                    coord.setLatitude(lat);
                    coord.setLongitude(lng);
                }
            }
            break;
        }
        case CoordinateSystem::WGS84_DMS: {
            // Parse DMS format - simplified implementation
            coord = QGeoCoordinate::fromString(coordString);
            break;
        }
        default:
            // For UTM and MGRS, would need more complex conversion
            break;
    }

    return coord;
}

QString MapWidget::convertCoordinate(const QGeoCoordinate& coord, CoordinateSystem from, CoordinateSystem to) {
    // For now, just format in the target system
    return formatCoordinate(coord, to);
}

// Enhanced procedure visualization methods
QJsonObject MapWidget::createEnhancedProcedureGeoJSON(const ProcedurePath& procedure) {
    QJsonObject featureCollection;
    featureCollection["type"] = "FeatureCollection";

    QJsonArray features;

    // Create main procedure line
    QJsonObject mainLine = procedureToGeoJSONObject(procedure);

    // Enhance with procedure-specific styling
    QJsonObject properties = mainLine["properties"].toObject();
    properties["procedureType"] = recordTypeToString(procedure.procedureType);
    properties["isMainPath"] = true;
    properties["sequenceStart"] = 1;
    properties["sequenceEnd"] = procedure.waypoints.size();

    // Add turn direction and leg type information if available
    QJsonArray legs;
    for (int i = 0; i < procedure.waypoints.size() - 1; ++i) {
        QJsonObject leg;
        leg["from"] = procedure.waypoints[i].getDisplayName();
        leg["to"] = procedure.waypoints[i + 1].getDisplayName();
        leg["sequence"] = i + 1;

        // Calculate bearing for directional arrows
        if (procedure.waypoints[i].coordinate.isValid() &&
            procedure.waypoints[i + 1].coordinate.isValid()) {
            double bearing = procedure.waypoints[i].coordinate.azimuthTo(procedure.waypoints[i + 1].coordinate);
            leg["bearing"] = bearing;
            leg["distance"] = procedure.waypoints[i].coordinate.distanceTo(procedure.waypoints[i + 1].coordinate) / 1852.0; // nautical miles
        }

        legs.append(leg);
    }
    properties["legs"] = legs;

    mainLine["properties"] = properties;
    features.append(mainLine);

    // Create individual waypoint features with sequence numbers
    for (int i = 0; i < procedure.waypoints.size(); ++i) {
        const auto& waypoint = procedure.waypoints[i];
        if (!waypoint.coordinate.isValid()) continue;

        QJsonObject waypointFeature;
        waypointFeature["type"] = "Feature";

        // Geometry
        QJsonObject geometry;
        geometry["type"] = "Point";
        QJsonArray coordinates;
        coordinates.append(waypoint.coordinate.longitude());
        coordinates.append(waypoint.coordinate.latitude());
        geometry["coordinates"] = coordinates;
        waypointFeature["geometry"] = geometry;

        // Properties
        QJsonObject wpProperties;
        wpProperties["id"] = waypoint.identifier + "_" + QString::number(i);
        wpProperties["name"] = waypoint.getDisplayName();
        wpProperties["sequence"] = i + 1;
        wpProperties["isWaypoint"] = true;
        wpProperties["procedureId"] = procedure.procedureId;
        wpProperties["procedureType"] = static_cast<int>(procedure.procedureType);
        wpProperties["isFirst"] = (i == 0);
        wpProperties["isLast"] = (i == procedure.waypoints.size() - 1);

        // Add waypoint-specific information
        if (!waypoint.routeType.isEmpty()) {
            wpProperties["routeType"] = waypoint.routeType;
        }

        waypointFeature["properties"] = wpProperties;
        features.append(waypointFeature);
    }

    featureCollection["features"] = features;
    return featureCollection;
}

void MapWidget::addEnhancedProcedure(const ProcedurePath& procedure) {
    if (!procedure.isValid() || !m_mapReady) return;

    QJsonObject enhancedGeoJSON = createEnhancedProcedureGeoJSON(procedure);
    QString geoJsonString = QJsonDocument(enhancedGeoJSON).toJson(QJsonDocument::Compact);

    QString style = getEnhancedProcedureStyle(procedure.procedureType);

    QString script = QString("addEnhancedProcedure(%1, %2);")
                    .arg(geoJsonString)
                    .arg(style);

    executeJavaScript(script);
}

QString MapWidget::getEnhancedProcedureStyle(RecordType type) {
    QJsonObject style;

    QString color = getRecordColor(type);

    // Base line style
    style["color"] = color;
    style["weight"] = 4;
    style["opacity"] = 0.9;

    // Procedure-specific styling
    switch (type) {
        case RecordType::SID:
            style["dashArray"] = "10, 5";
            style["arrowColor"] = "#FF9500";
            style["arrowSize"] = 12;
            break;
        case RecordType::STAR:
            style["dashArray"] = "5, 10";
            style["arrowColor"] = "#00C851";
            style["arrowSize"] = 12;
            break;
        case RecordType::APPROACH:
            style["dashArray"] = "";
            style["arrowColor"] = "#FF4444";
            style["arrowSize"] = 14;
            break;
        default:
            style["dashArray"] = "";
            style["arrowColor"] = color;
            style["arrowSize"] = 10;
            break;
    }

    // Waypoint styling
    QJsonObject waypointStyle;
    waypointStyle["radius"] = 6;
    waypointStyle["fillColor"] = color;
    waypointStyle["color"] = "#ffffff";
    waypointStyle["weight"] = 2;
    waypointStyle["opacity"] = 1.0;
    waypointStyle["fillOpacity"] = 0.9;

    // Special styling for first and last waypoints
    QJsonObject firstWaypointStyle = waypointStyle;
    firstWaypointStyle["radius"] = 8;
    firstWaypointStyle["fillColor"] = "#2ecc71";

    QJsonObject lastWaypointStyle = waypointStyle;
    lastWaypointStyle["radius"] = 8;
    lastWaypointStyle["fillColor"] = "#e74c3c";

    style["waypointStyle"] = waypointStyle;
    style["firstWaypointStyle"] = firstWaypointStyle;
    style["lastWaypointStyle"] = lastWaypointStyle;

    // Animation settings
    style["enableAnimation"] = m_settings.enableAnimation;
    style["animationDuration"] = 2000;

    return QJsonDocument(style).toJson(QJsonDocument::Compact);
}

// Add method to update procedure visualization based on zoom level
void MapWidget::updateProcedureDetailLevel(int zoomLevel) {
    QString detailLevel;
    bool showWaypoints = true;
    bool showSequenceNumbers = true;
    bool showArrows = true;

    if (zoomLevel < 8) {
        detailLevel = "low";
        showWaypoints = false;
        showSequenceNumbers = false;
        showArrows = false;
    } else if (zoomLevel < 12) {
        detailLevel = "medium";
        showWaypoints = true;
        showSequenceNumbers = false;
        showArrows = true;
    } else {
        detailLevel = "high";
        showWaypoints = true;
        showSequenceNumbers = true;
        showArrows = true;
    }

    QString script = QString("updateProcedureDetailLevel('%1', %2, %3, %4);")
                    .arg(detailLevel)
                    .arg(showWaypoints ? "true" : "false")
                    .arg(showSequenceNumbers ? "true" : "false")
                    .arg(showArrows ? "true" : "false");

    executeJavaScript(script);
}

// Additional interactive features
void MapWidget::highlightProcedure(const QString& procedureId, bool highlight) {
    if (!m_mapReady) return;

    executeJavaScript(QString("highlightProcedure('%1', %2);")
                     .arg(procedureId)
                     .arg(highlight ? "true" : "false"));
}

void MapWidget::zoomToRecord(const QString& identifier) {
    if (!m_mapReady) return;

    // Find record in cache
    if (m_recordCache.contains(identifier)) {
        const ArincRecord& record = m_recordCache[identifier];
        if (record.coordinate.isValid()) {
            setMapCenter(record.coordinate, 15);
        }
    }
}

void MapWidget::zoomToProcedure(const QString& procedureId) {
    if (!m_mapReady) return;

    // Find procedure in cache
    if (m_procedureCache.contains(procedureId)) {
        const ProcedurePath& procedure = m_procedureCache[procedureId];
        QVector<ProcedurePath> singleProcedure;
        singleProcedure.append(procedure);
        zoomToExtent(singleProcedure);
    }
}

void MapWidget::showRecordDetails(const QString& identifier) {
    if (m_recordCache.contains(identifier)) {
        const ArincRecord& record = m_recordCache[identifier];
        emit recordClicked(record);

        // Also zoom to the record
        zoomToRecord(identifier);
    }
}

void MapWidget::showProcedureDetails(const QString& procedureId) {
    if (m_procedureCache.contains(procedureId)) {
        const ProcedurePath& procedure = m_procedureCache[procedureId];
        emit procedureClicked(procedure);

        // Also zoom to the procedure
        zoomToProcedure(procedureId);
    }
}

void MapWidget::enableMeasurementMode(bool enable) {
    if (!m_mapReady) return;

    if (enable) {
        m_measureDistanceBtn->setEnabled(true);
        m_measureAreaBtn->setEnabled(true);
        executeJavaScript("enableMeasurementMode(true);");
    } else {
        executeJavaScript("enableMeasurementMode(false);");
        executeJavaScript("clearMeasurements();");
        m_measurementLabel->setText("Measurement: --");
    }
}

void MapWidget::setMapCursor(const QString& cursor) {
    if (!m_mapReady) return;

    executeJavaScript(QString("setMapCursor('%1');").arg(cursor));
}

void MapWidget::addCustomMarker(const QGeoCoordinate& coordinate, const QString& title, const QString& description, const QString& icon) {
    if (!coordinate.isValid() || !m_mapReady) return;

    QString script = QString("addCustomMarker(%1, %2, '%3', '%4', '%5');")
                    .arg(coordinate.latitude())
                    .arg(coordinate.longitude())
                    .arg(title.replace("'", "\\'"))
                    .arg(description.replace("'", "\\'"))
                    .arg(icon);

    executeJavaScript(script);
}

void MapWidget::removeCustomMarker(const QString& markerId) {
    if (!m_mapReady) return;

    executeJavaScript(QString("removeCustomMarker('%1');").arg(markerId));
}

void MapWidget::clearCustomMarkers() {
    if (!m_mapReady) return;

    executeJavaScript("clearCustomMarkers();");
}

QString MapWidget::getMapExtentAsString() const {
    if (!m_mapReady) return QString();

    return QString("North: %1, South: %2, East: %3, West: %4")
           .arg(m_viewportNorth, 0, 'f', 6)
           .arg(m_viewportSouth, 0, 'f', 6)
           .arg(m_viewportEast, 0, 'f', 6)
           .arg(m_viewportWest, 0, 'f', 6);
}

void MapWidget::saveMapState() {
    if (!m_mapReady) return;

    // Save current map state (center, zoom, layer visibility)
    QVariant result = executeJavaScriptSync("getMapState();");
    QVariantMap state = result.toMap();

    // Store in settings or emit signal for external handling
    emit mapStateChanged(state);
}

void MapWidget::restoreMapState(const QVariantMap& state) {
    if (!m_mapReady || state.isEmpty()) return;

    QString stateJson = QJsonDocument::fromVariant(state).toJson(QJsonDocument::Compact);
    executeJavaScript(QString("restoreMapState(%1);").arg(stateJson));
}

void MapWidget::printMap() {
    if (!m_mapReady) return;

    // Trigger print dialog for the web view
    m_webView->page()->triggerAction(QWebEnginePage::Print);
}

void MapWidget::copyCoordinatesToClipboard() {
    QString coords = getCurrentCoordinates();
    if (!coords.isEmpty()) {
        QApplication::clipboard()->setText(coords);
        m_statusLabel->setText("Coordinates copied to clipboard");
        QTimer::singleShot(2000, [this]() {
            m_statusLabel->setText("Ready");
        });
    }
}

// Performance optimization implementations
void MapWidget::enableViewportCulling(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enableViewportCulling(%1);").arg(enable ? "true" : "false"));
}

void MapWidget::setMaxVisibleRecords(int maxRecords) {
    if (!m_mapReady) return;

    executeJavaScript(QString("setMaxVisibleRecords(%1);").arg(maxRecords));
}

void MapWidget::enableProgressiveLoading(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enableProgressiveLoading(%1);").arg(enable ? "true" : "false"));
}

void MapWidget::setRenderingQuality(const QString& quality) {
    if (!m_mapReady) return;

    // quality can be "low", "medium", "high"
    executeJavaScript(QString("setRenderingQuality('%1');").arg(quality));
}

void MapWidget::optimizeMemoryUsage() {
    // Clear caches of records not in viewport
    QSet<QString> recordsToKeep;
    QSet<QString> proceduresToKeep;

    // Keep only records in current viewport
    for (auto it = m_recordCache.begin(); it != m_recordCache.end();) {
        if (isInViewport(it.value().coordinate)) {
            recordsToKeep.insert(it.key());
            ++it;
        } else {
            it = m_recordCache.erase(it);
        }
    }

    // Keep only procedures with waypoints in viewport
    for (auto it = m_procedureCache.begin(); it != m_procedureCache.end();) {
        bool hasVisibleWaypoints = false;
        for (const auto& waypoint : it.value().waypoints) {
            if (isInViewport(waypoint.coordinate)) {
                hasVisibleWaypoints = true;
                break;
            }
        }

        if (hasVisibleWaypoints) {
            proceduresToKeep.insert(it.key());
            ++it;
        } else {
            it = m_procedureCache.erase(it);
        }
    }

    // Update visible sets
    m_visibleRecords = recordsToKeep;
    m_visibleProcedures = proceduresToKeep;

    // Trigger JavaScript garbage collection
    executeJavaScript("if (window.gc) window.gc();");

    qDebug() << "Memory optimization: keeping" << recordsToKeep.size() << "records and"
             << proceduresToKeep.size() << "procedures";
}

void MapWidget::enableLevelOfDetail(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enableLevelOfDetail(%1);").arg(enable ? "true" : "false"));
}

void MapWidget::setUpdateThrottleInterval(int milliseconds) {
    m_updateTimer->setInterval(qMax(50, milliseconds)); // Minimum 50ms
}

void MapWidget::preloadAdjacentTiles() {
    if (!m_mapReady) return;

    executeJavaScript("preloadAdjacentTiles();");
}

void MapWidget::enableTileCache(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enableTileCache(%1);").arg(enable ? "true" : "false"));
}

void MapWidget::clearTileCache() {
    if (!m_mapReady) return;

    executeJavaScript("clearTileCache();");
}

void MapWidget::setDataStreamingEnabled(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("setDataStreamingEnabled(%1);").arg(enable ? "true" : "false"));
}

void MapWidget::enableWebGLRendering(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enableWebGLRendering(%1);").arg(enable ? "true" : "false"));
}

QVariantMap MapWidget::getPerformanceMetrics() const {
    if (!m_mapReady) return QVariantMap();

    QVariant result = executeJavaScriptSync("getPerformanceMetrics();");
    return result.toMap();
}

void MapWidget::enablePerformanceMonitoring(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enablePerformanceMonitoring(%1);").arg(enable ? "true" : "false"));
}

// Adaptive quality based on performance
void MapWidget::enableAdaptiveQuality(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enableAdaptiveQuality(%1);").arg(enable ? "true" : "false"));
}

void MapWidget::setPerformanceTarget(int targetFPS) {
    if (!m_mapReady) return;

    executeJavaScript(QString("setPerformanceTarget(%1);").arg(targetFPS));
}

// Batch operations for better performance
void MapWidget::beginBatchUpdate() {
    if (!m_mapReady) return;

    executeJavaScript("beginBatchUpdate();");
}

void MapWidget::endBatchUpdate() {
    if (!m_mapReady) return;

    executeJavaScript("endBatchUpdate();");
}

void MapWidget::suspendRendering() {
    if (!m_mapReady) return;

    executeJavaScript("suspendRendering();");
}

void MapWidget::resumeRendering() {
    if (!m_mapReady) return;

    executeJavaScript("resumeRendering();");
}

// Testing and validation methods
bool MapWidget::validateCoordinateAccuracy(const QVector<ArincRecord>& testRecords) {
    bool allValid = true;
    int validCount = 0;
    int invalidCount = 0;

    for (const auto& record : testRecords) {
        if (record.coordinate.isValid()) {
            // Check coordinate bounds
            double lat = record.coordinate.latitude();
            double lng = record.coordinate.longitude();

            if (lat >= -90.0 && lat <= 90.0 && lng >= -180.0 && lng <= 180.0) {
                validCount++;
            } else {
                invalidCount++;
                allValid = false;
                qWarning() << "Invalid coordinate for record" << record.identifier
                          << ":" << lat << "," << lng;
            }
        } else {
            invalidCount++;
            allValid = false;
            qWarning() << "Invalid coordinate object for record" << record.identifier;
        }
    }

    qDebug() << "Coordinate validation: " << validCount << "valid," << invalidCount << "invalid";
    return allValid;
}

QVariantMap MapWidget::runPerformanceTest(const QVector<ArincRecord>& testData) {
    QVariantMap results;

    if (!m_mapReady) {
        results["error"] = "Map not ready";
        return results;
    }

    // Test data loading performance
    QElapsedTimer timer;
    timer.start();

    plotRecords(testData);

    qint64 loadTime = timer.elapsed();
    results["loadTime"] = loadTime;
    results["recordCount"] = testData.size();
    results["recordsPerSecond"] = (testData.size() * 1000.0) / loadTime;

    // Test rendering performance
    timer.restart();

    // Zoom operations
    if (!testData.isEmpty()) {
        zoomToExtent(testData);
    }

    qint64 renderTime = timer.elapsed();
    results["renderTime"] = renderTime;

    // Memory usage
    results["cacheSize"] = m_recordCache.size();
    results["visibleRecords"] = m_visibleRecords.size();

    // JavaScript performance metrics
    QVariantMap jsMetrics = getPerformanceMetrics();
    results["jsMetrics"] = jsMetrics;

    qDebug() << "Performance test results:" << results;
    return results;
}

bool MapWidget::validateMapFunctionality() {
    if (!m_mapReady) {
        qWarning() << "Map not ready for validation";
        return false;
    }

    bool allTestsPassed = true;

    // Test 1: Basic map operations
    QGeoCoordinate testCoord(45.0, -100.0);
    setMapCenter(testCoord, 10);

    QString currentCoords = getCurrentCoordinates();
    if (currentCoords.isEmpty()) {
        qWarning() << "Test failed: getCurrentCoordinates returned empty string";
        allTestsPassed = false;
    }

    // Test 2: Layer operations
    setLayerVisible(MapLayer::WAYPOINTS, false);
    if (isLayerVisible(MapLayer::WAYPOINTS)) {
        qWarning() << "Test failed: Layer visibility not updated correctly";
        allTestsPassed = false;
    }

    setLayerVisible(MapLayer::WAYPOINTS, true);
    if (!isLayerVisible(MapLayer::WAYPOINTS)) {
        qWarning() << "Test failed: Layer visibility not restored correctly";
        allTestsPassed = false;
    }

    // Test 3: Coordinate system conversion
    QString decimalCoords = formatCoordinate(testCoord, CoordinateSystem::WGS84_DECIMAL);
    QString dmsCoords = formatCoordinate(testCoord, CoordinateSystem::WGS84_DMS);

    if (decimalCoords.isEmpty() || dmsCoords.isEmpty()) {
        qWarning() << "Test failed: Coordinate formatting failed";
        allTestsPassed = false;
    }

    // Test 4: Measurement functionality
    double distance = measureDistance(QGeoCoordinate(45.0, -100.0), QGeoCoordinate(46.0, -100.0));
    if (distance <= 0) {
        qWarning() << "Test failed: Distance measurement failed";
        allTestsPassed = false;
    }

    // Test 5: Export functionality (basic validation)
    QString testGeoJSON = recordsToGeoJSON(QVector<ArincRecord>());
    if (!testGeoJSON.contains("FeatureCollection")) {
        qWarning() << "Test failed: GeoJSON export failed";
        allTestsPassed = false;
    }

    qDebug() << "Map functionality validation:" << (allTestsPassed ? "PASSED" : "FAILED");
    return allTestsPassed;
}

void MapWidget::runStressTest(int recordCount) {
    qDebug() << "Starting stress test with" << recordCount << "records";

    // Generate test data
    QVector<ArincRecord> testRecords;
    testRecords.reserve(recordCount);

    for (int i = 0; i < recordCount; ++i) {
        ArincRecord record;
        record.type = static_cast<RecordType>(30 + (i % 5)); // Vary record types
        record.identifier = QString("TEST_%1").arg(i, 6, 10, QChar('0'));
        record.name = QString("Test Record %1").arg(i);
        record.customerAreaCode = "TST";

        // Generate random coordinates within reasonable bounds
        double lat = -90.0 + (qrand() % 18000) / 100.0; // -90 to 90
        double lng = -180.0 + (qrand() % 36000) / 100.0; // -180 to 180
        record.coordinate = QGeoCoordinate(lat, lng);

        testRecords.append(record);
    }

    // Run performance test
    QVariantMap results = runPerformanceTest(testRecords);

    // Check performance thresholds
    qint64 loadTime = results["loadTime"].toLongLong();
    double recordsPerSecond = results["recordsPerSecond"].toDouble();

    qDebug() << "Stress test results:";
    qDebug() << "  Load time:" << loadTime << "ms";
    qDebug() << "  Records per second:" << recordsPerSecond;
    qDebug() << "  Memory usage:" << results["cacheSize"].toInt() << "cached records";

    // Performance thresholds
    if (recordsPerSecond < 100) {
        qWarning() << "Performance warning: Low records per second rate";
    }

    if (loadTime > recordCount * 10) { // More than 10ms per record is concerning
        qWarning() << "Performance warning: High load time";
    }

    // Clean up
    clearMap();
    optimizeMemoryUsage();

    qDebug() << "Stress test completed";
}

void MapWidget::validateDataIntegrity(const QVector<ArincRecord>& records, const QVector<ProcedurePath>& procedures) {
    qDebug() << "Validating data integrity...";

    int validRecords = 0;
    int invalidRecords = 0;
    int validProcedures = 0;
    int invalidProcedures = 0;

    // Validate records
    for (const auto& record : records) {
        if (record.isValid() && record.coordinate.isValid()) {
            validRecords++;
        } else {
            invalidRecords++;
            qDebug() << "Invalid record:" << record.identifier;
        }
    }

    // Validate procedures
    for (const auto& procedure : procedures) {
        if (procedure.isValid()) {
            validProcedures++;

            // Check waypoint sequence
            for (int i = 0; i < procedure.waypoints.size(); ++i) {
                const auto& waypoint = procedure.waypoints[i];
                if (!waypoint.coordinate.isValid()) {
                    qWarning() << "Invalid waypoint in procedure" << procedure.procedureId
                              << "at sequence" << i;
                }
            }
        } else {
            invalidProcedures++;
            qDebug() << "Invalid procedure:" << procedure.procedureId;
        }
    }

    qDebug() << "Data integrity validation:";
    qDebug() << "  Records: " << validRecords << "valid," << invalidRecords << "invalid";
    qDebug() << "  Procedures: " << validProcedures << "valid," << invalidProcedures << "invalid";

    // Calculate success rate
    double recordSuccessRate = (double)validRecords / (validRecords + invalidRecords) * 100.0;
    double procedureSuccessRate = (double)validProcedures / (validProcedures + invalidProcedures) * 100.0;

    qDebug() << "  Success rates: Records" << recordSuccessRate << "%, Procedures" << procedureSuccessRate << "%";

    // Emit results
    QVariantMap results;
    results["validRecords"] = validRecords;
    results["invalidRecords"] = invalidRecords;
    results["validProcedures"] = validProcedures;
    results["invalidProcedures"] = invalidProcedures;
    results["recordSuccessRate"] = recordSuccessRate;
    results["procedureSuccessRate"] = procedureSuccessRate;

    emit dataValidationCompleted(results);
}

void MapWidget::enableDebugMode(bool enable) {
    if (!m_mapReady) return;

    executeJavaScript(QString("enableDebugMode(%1);").arg(enable ? "true" : "false"));

    if (enable) {
        qDebug() << "Debug mode enabled";
        enablePerformanceMonitoring(true);
    } else {
        qDebug() << "Debug mode disabled";
        enablePerformanceMonitoring(false);
    }
}
