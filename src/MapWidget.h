/**
 * @file MapWidget.h
 * @brief Enhanced map widget for ARINC 424 aviation data visualization
 *
 * This file contains a completely rewritten map widget with advanced features:
 * - High-performance coordinate plotting with clustering
 * - Layer management for different record types
 * - Interactive controls and measurement tools
 * - Enhanced procedure visualization with directional arrows
 * - Export capabilities and coordinate system support
 */

#pragma once

#include <QWidget>
#include <QWebEngineView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGeoCoordinate>
#include <QVector>
#include <QTimer>
#include <QProgressBar>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QComboBox>
#include <QSlider>
#include <QGroupBox>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QHash>
#include <QSet>
#include <QMutex>
#include <QThread>
#include <QFuture>
#include <QtConcurrent>
#include "DataModels.h"

/**
 * @enum MapLayer
 * @brief Enumeration of map layers for organized data display
 */
enum class MapLayer {
    NAVAIDS,        ///< VHF/NDB Navigation Aids
    WAYPOINTS,      ///< Waypoints and Fixes
    AIRPORTS,       ///< Airports and Heliports
    AIRWAYS,        ///< Airways and Routes
    PROCEDURES,     ///< SID/STAR/Approaches
    AIRSPACE,       ///< Controlled/Restricted Airspace
    TERRAIN,        ///< Terrain and Obstacles
    WEATHER         ///< Weather overlays
};

/**
 * @enum CoordinateSystem
 * @brief Supported coordinate systems for display
 */
enum class CoordinateSystem {
    WGS84_DECIMAL,  ///< WGS84 Decimal Degrees
    WGS84_DMS,      ///< WGS84 Degrees/Minutes/Seconds
    UTM,            ///< Universal Transverse Mercator
    MGRS            ///< Military Grid Reference System
};

/**
 * @struct MapSettings
 * @brief Configuration settings for map display
 */
struct MapSettings {
    bool enableClustering = true;
    int clusterRadius = 50;
    int maxZoomCluster = 15;
    bool showCoordinates = true;
    bool showScale = true;
    bool showMeasurement = false;
    CoordinateSystem coordSystem = CoordinateSystem::WGS84_DECIMAL;
    QString basemap = "OpenStreetMap";
    double opacity = 1.0;
    bool showLayerControl = true;
    bool enableAnimation = true;
};

/**
 * @class MapWidget
 * @brief Enhanced map widget for aviation data visualization
 *
 * This completely rewritten map widget provides:
 * - High-performance rendering of large datasets
 * - Advanced layer management and filtering
 * - Interactive measurement and navigation tools
 * - Export capabilities for maps and data
 * - Real-time coordinate display and conversion
 */
class MapWidget : public QWidget {
    Q_OBJECT

public:
    explicit MapWidget(QWidget* parent = nullptr);
    ~MapWidget();

    // Core functionality
    void initializeMap();
    void clearMap();
    void clearLayer(MapLayer layer);

    // Data plotting with enhanced performance
    void plotRecords(const QVector<ArincRecord>& records);
    void plotProcedures(const QVector<ProcedurePath>& procedures);
    void addRecord(const ArincRecord& record);
    void addProcedure(const ProcedurePath& procedure);
    void updateRecord(const ArincRecord& record);
    void removeRecord(const QString& identifier);

    // Map navigation and control
    void setMapCenter(const QGeoCoordinate& center, int zoom = 8);
    void zoomToExtent(const QVector<ArincRecord>& records);
    void zoomToExtent(const QVector<ProcedurePath>& procedures);
    void zoomToAirport(const QString& airportCode);
    void fitBounds(double north, double south, double east, double west);

    // Layer management
    void setLayerVisible(MapLayer layer, bool visible);
    bool isLayerVisible(MapLayer layer) const;
    void setLayerOpacity(MapLayer layer, double opacity);
    double getLayerOpacity(MapLayer layer) const;

    // Settings and configuration
    void setMapSettings(const MapSettings& settings);
    MapSettings getMapSettings() const;
    void setBasemap(const QString& basemap);
    void setCoordinateSystem(CoordinateSystem system);

    // Export and utilities
    void exportMap(const QString& filename, const QString& format = "PNG");
    void exportData(const QString& filename, const QString& format = "GeoJSON");
    QString getCurrentCoordinates() const;
    double measureDistance(const QGeoCoordinate& start, const QGeoCoordinate& end) const;

    // Interactive features
    void highlightProcedure(const QString& procedureId, bool highlight = true);
    void zoomToRecord(const QString& identifier);
    void zoomToProcedure(const QString& procedureId);
    void showRecordDetails(const QString& identifier);
    void showProcedureDetails(const QString& procedureId);
    void enableMeasurementMode(bool enable);
    void setMapCursor(const QString& cursor);
    void addCustomMarker(const QGeoCoordinate& coordinate, const QString& title,
                        const QString& description = "", const QString& icon = "marker");
    void removeCustomMarker(const QString& markerId);
    void clearCustomMarkers();
    QString getMapExtentAsString() const;
    void saveMapState();
    void restoreMapState(const QVariantMap& state);
    void printMap();
    void copyCoordinatesToClipboard();

    // Performance optimization methods
    void enableViewportCulling(bool enable);
    void setMaxVisibleRecords(int maxRecords);
    void enableProgressiveLoading(bool enable);
    void setRenderingQuality(const QString& quality);
    void optimizeMemoryUsage();
    void enableLevelOfDetail(bool enable);
    void setUpdateThrottleInterval(int milliseconds);
    void preloadAdjacentTiles();
    void enableTileCache(bool enable);
    void clearTileCache();
    void setDataStreamingEnabled(bool enable);
    void enableWebGLRendering(bool enable);
    QVariantMap getPerformanceMetrics() const;
    void enablePerformanceMonitoring(bool enable);
    void enableAdaptiveQuality(bool enable);
    void setPerformanceTarget(int targetFPS);
    void beginBatchUpdate();
    void endBatchUpdate();
    void suspendRendering();
    void resumeRendering();

    // Testing and validation methods
    bool validateCoordinateAccuracy(const QVector<ArincRecord>& testRecords);
    QVariantMap runPerformanceTest(const QVector<ArincRecord>& testData);
    bool validateMapFunctionality();
    void runStressTest(int recordCount = 10000);
    void validateDataIntegrity(const QVector<ArincRecord>& records, const QVector<ProcedurePath>& procedures);
    void enableDebugMode(bool enable);

public slots:
    void onRecordsUpdated(const QVector<ArincRecord>& records);
    void onProceduresUpdated(const QVector<ProcedurePath>& procedures);
    void onFilterChanged();
    void onLayerToggled(MapLayer layer, bool visible);
    void onOpacityChanged(MapLayer layer, double opacity);
    void onBasemapChanged(const QString& basemap);
    void onCoordinateSystemChanged(CoordinateSystem system);

signals:
    void mapReady();
    void recordClicked(const ArincRecord& record);
    void procedureClicked(const ProcedurePath& procedure);
    void coordinatesChanged(const QString& coordinates);
    void extentChanged(double north, double south, double east, double west);
    void layerVisibilityChanged(MapLayer layer, bool visible);
    void loadingProgress(int percentage);
    void errorOccurred(const QString& error);
    void mapStateChanged(const QVariantMap& state);
    void dataValidationCompleted(const QVariantMap& results);

private slots:
    void onMapLoadFinished(bool success);
    void onJavaScriptResult(const QVariant& result);
    void onUpdateTimer();
    void onDataProcessingFinished();

private:
    // UI setup and management
    void setupUI();
    void setupControls();
    void setupLayerControls();
    void setupMeasurementTools();
    void createMapHTML();

    // Data processing and conversion
    QString generateMapHTML();
    QString recordsToGeoJSON(const QVector<ArincRecord>& records);
    QString proceduresToGeoJSON(const QVector<ProcedurePath>& procedures);
    QString recordToGeoJSON(const ArincRecord& record);
    QString procedureToGeoJSON(const ProcedurePath& procedure);
    QJsonObject recordToGeoJSONObject(const ArincRecord& record);
    QJsonObject procedureToGeoJSONObject(const ProcedurePath& procedure);

    // Enhanced procedure methods
    QJsonObject createEnhancedProcedureGeoJSON(const ProcedurePath& procedure);
    void addEnhancedProcedure(const ProcedurePath& procedure);
    QString getEnhancedProcedureStyle(RecordType type);
    void updateProcedureDetailLevel(int zoomLevel);

    // Styling and visualization
    QString getRecordStyle(RecordType type);
    QString getRecordIcon(RecordType type);
    QString getRecordColor(RecordType type);
    QString getProcedureStyle(RecordType type);
    QString formatPopupContent(const ArincRecord& record);
    QString formatPopupContent(const ProcedurePath& procedure);
    int getRecordRadius(RecordType type);
    QString recordTypeToString(RecordType type);
    QString mapLayerToString(MapLayer layer);
    QString getLayerForRecordType(RecordType type);

    // Performance optimization
    void processDataAsync(const QVector<ArincRecord>& records);
    void processRecordsImmediate(const QVector<ArincRecord>& records);
    void updateViewport();
    void optimizeForZoomLevel(int zoomLevel);
    bool isInViewport(const QGeoCoordinate& coord);

    // Export helpers
    void exportGeoJSON(const QString& filename);
    void exportKML(const QString& filename);

    // UI helpers
    QWidget* createControlsWidget();
    void setupControls();

    // Coordinate system utilities
    QString formatCoordinate(const QGeoCoordinate& coord, CoordinateSystem system);
    QGeoCoordinate parseCoordinate(const QString& coordString, CoordinateSystem system);
    QString convertCoordinate(const QGeoCoordinate& coord, CoordinateSystem from, CoordinateSystem to);

    // JavaScript interface
    void executeJavaScript(const QString& script);
    void executeJavaScriptAsync(const QString& script);
    QVariant executeJavaScriptSync(const QString& script);

    // UI Components
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_controlLayout;
    QWebEngineView* m_webView;

    // Control panels
    QGroupBox* m_layerControlGroup;
    QGroupBox* m_settingsGroup;
    QGroupBox* m_measurementGroup;

    // Layer controls
    QHash<MapLayer, QCheckBox*> m_layerCheckboxes;
    QHash<MapLayer, QSlider*> m_opacitySliders;

    // Settings controls
    QComboBox* m_basemapCombo;
    QComboBox* m_coordSystemCombo;
    QCheckBox* m_clusteringCheckbox;
    QCheckBox* m_animationCheckbox;
    QSlider* m_clusterRadiusSlider;

    // Measurement tools
    QPushButton* m_measureDistanceBtn;
    QPushButton* m_measureAreaBtn;
    QPushButton* m_clearMeasurementsBtn;
    QLabel* m_coordinateLabel;
    QLabel* m_measurementLabel;

    // Status and progress
    QProgressBar* m_loadingProgress;
    QLabel* m_statusLabel;
    QPushButton* m_exportMapBtn;
    QPushButton* m_exportDataBtn;

    // Data and state management
    QVector<ArincRecord> m_currentRecords;
    QVector<ProcedurePath> m_currentProcedures;
    QHash<QString, ArincRecord> m_recordCache;
    QHash<QString, ProcedurePath> m_procedureCache;

    MapSettings m_settings;
    QHash<MapLayer, bool> m_layerVisibility;
    QHash<MapLayer, double> m_layerOpacity;

    // Performance and threading
    QTimer* m_updateTimer;
    QMutex m_dataMutex;
    QFuture<void> m_dataProcessingFuture;
    bool m_mapReady;
    bool m_dataProcessing;
    int m_currentZoom;
    QGeoCoordinate m_currentCenter;

    // Viewport optimization
    double m_viewportNorth, m_viewportSouth, m_viewportEast, m_viewportWest;
    QSet<QString> m_visibleRecords;
    QSet<QString> m_visibleProcedures;
};
